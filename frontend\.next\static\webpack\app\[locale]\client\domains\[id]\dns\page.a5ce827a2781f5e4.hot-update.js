"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/constants/dnsRecords.js":
/*!*************************************!*\
  !*** ./src/constants/dnsRecords.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_TTL: function() { return /* binding */ DEFAULT_TTL; },\n/* harmony export */   DNS_PRESETS: function() { return /* binding */ DNS_PRESETS; },\n/* harmony export */   DNS_RECORD_TYPES: function() { return /* binding */ DNS_RECORD_TYPES; },\n/* harmony export */   TTL_OPTIONS: function() { return /* binding */ TTL_OPTIONS; },\n/* harmony export */   checkRecordRestrictions: function() { return /* binding */ checkRecordRestrictions; },\n/* harmony export */   getDnsRecordTypes: function() { return /* binding */ getDnsRecordTypes; },\n/* harmony export */   validateDnsRecord: function() { return /* binding */ validateDnsRecord; }\n/* harmony export */ });\n// DNS Record Types and Configuration\nconst DNS_RECORD_TYPES = {\n    A: {\n        name: \"A\",\n        label: \"A Record\",\n        description: \"Points a domain to an IPv4 address\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, www, mail, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"IPv4 Address\",\n                type: \"text\",\n                required: true,\n                placeholder: \"***********\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"number\",\n                required: true,\n                default: 14400,\n                placeholder: \"14400\",\n                min: 60,\n                max: 2147483647\n            }\n        ],\n        validation: {\n            content: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/\n        }\n    },\n    AAAA: {\n        name: \"AAAA\",\n        label: \"AAAA Record\",\n        description: \"Points a domain to an IPv6 address\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, www, mail, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"IPv6 Address\",\n                type: \"text\",\n                required: true,\n                placeholder: \"2001:0db8:85a3:0000:0000:8a2e:0370:7334\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"number\",\n                required: true,\n                default: 14400,\n                placeholder: \"14400\",\n                min: 60,\n                max: 2147483647\n            }\n        ],\n        validation: {\n            content: /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/\n        }\n    },\n    CNAME: {\n        name: \"CNAME\",\n        label: \"CNAME Record\",\n        description: \"Points a domain to another domain name\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"www, blog, shop, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"Target Domain\",\n                type: \"text\",\n                required: true,\n                placeholder: \"example.com\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"number\",\n                required: true,\n                default: 14400,\n                placeholder: \"14400\",\n                min: 60,\n                max: 2147483647\n            }\n        ],\n        validation: {\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/\n        },\n        restrictions: {\n            nameCannotBe: [\n                \"@\"\n            ],\n            message: \"CNAME records cannot be used for the root domain (@). Use A or AAAA records instead.\"\n        }\n    },\n    MX: {\n        name: \"MX\",\n        label: \"MX Record\",\n        description: \"Specifies mail servers for the domain\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, mail, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"Mail Server\",\n                type: \"text\",\n                required: true,\n                placeholder: \"mail.example.com\"\n            },\n            {\n                name: \"priority\",\n                label: \"Priority\",\n                type: \"number\",\n                required: true,\n                placeholder: \"10\",\n                min: 0,\n                max: 65535\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/,\n            priority: /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/\n        }\n    },\n    TXT: {\n        name: \"TXT\",\n        label: \"TXT Record\",\n        description: \"Stores text information for various purposes (SPF, DKIM, verification, etc.)\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, _dmarc, _spf, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"Text Content\",\n                type: \"textarea\",\n                required: true,\n                placeholder: \"v=spf1 include:_spf.google.com ~all\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^.{1,255}$/\n        }\n    },\n    NS: {\n        name: \"NS\",\n        label: \"NS Record\",\n        description: \"Delegates a subdomain to different nameservers\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Subdomain\",\n                type: \"text\",\n                required: true,\n                placeholder: \"subdomain\"\n            },\n            {\n                name: \"content\",\n                label: \"Nameserver\",\n                type: \"text\",\n                required: true,\n                placeholder: \"ns1.example.com\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/\n        },\n        restrictions: {\n            nameCannotBe: [\n                \"@\"\n            ],\n            message: \"NS records for the root domain (@) should be managed through nameserver settings, not DNS records.\"\n        }\n    },\n    SRV: {\n        name: \"SRV\",\n        label: \"SRV Record\",\n        description: \"Specifies the location of services (port and hostname)\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Service Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"_service._protocol (e.g., _sip._tcp)\"\n            },\n            {\n                name: \"content\",\n                label: \"Target Host\",\n                type: \"text\",\n                required: true,\n                placeholder: \"target.example.com\"\n            },\n            {\n                name: \"priority\",\n                label: \"Priority\",\n                type: \"number\",\n                required: true,\n                placeholder: \"10\",\n                min: 0,\n                max: 65535\n            },\n            {\n                name: \"weight\",\n                label: \"Weight\",\n                type: \"number\",\n                required: true,\n                placeholder: \"5\",\n                min: 0,\n                max: 65535\n            },\n            {\n                name: \"port\",\n                label: \"Port\",\n                type: \"number\",\n                required: true,\n                placeholder: \"5060\",\n                min: 1,\n                max: 65535\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            name: /^_[a-zA-Z0-9-]+\\._[a-zA-Z0-9-]+$/,\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/,\n            priority: /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/,\n            weight: /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/,\n            port: /^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/\n        }\n    }\n};\n// TTL (Time To Live) options in seconds\nconst TTL_OPTIONS = [\n    {\n        value: 300,\n        label: \"5 minutes\"\n    },\n    {\n        value: 600,\n        label: \"10 minutes\"\n    },\n    {\n        value: 1800,\n        label: \"30 minutes\"\n    },\n    {\n        value: 3600,\n        label: \"1 hour\"\n    },\n    {\n        value: 7200,\n        label: \"2 hours\"\n    },\n    {\n        value: 14400,\n        label: \"4 hours\"\n    },\n    {\n        value: 28800,\n        label: \"8 hours\"\n    },\n    {\n        value: 43200,\n        label: \"12 hours\"\n    },\n    {\n        value: 86400,\n        label: \"1 day\"\n    },\n    {\n        value: 172800,\n        label: \"2 days\"\n    },\n    {\n        value: 604800,\n        label: \"1 week\"\n    }\n];\n// Default TTL value\nconst DEFAULT_TTL = 14400;\n// Common DNS record presets for quick setup\nconst DNS_PRESETS = {\n    \"Google Workspace\": {\n        description: \"Configure Google Workspace email and services\",\n        records: [\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"aspmx.l.google.com\",\n                priority: 1,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt1.aspmx.l.google.com\",\n                priority: 5,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt2.aspmx.l.google.com\",\n                priority: 5,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt3.aspmx.l.google.com\",\n                priority: 10,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt4.aspmx.l.google.com\",\n                priority: 10,\n                ttl: 14400\n            },\n            {\n                type: \"TXT\",\n                name: \"@\",\n                content: \"v=spf1 include:_spf.google.com ~all\",\n                ttl: 14400\n            }\n        ]\n    },\n    \"Microsoft 365\": {\n        description: \"Configure Microsoft 365 email and services\",\n        records: [\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"{domain}.mail.protection.outlook.com\",\n                priority: 0,\n                ttl: 14400\n            },\n            {\n                type: \"TXT\",\n                name: \"@\",\n                content: \"v=spf1 include:spf.protection.outlook.com -all\",\n                ttl: 14400\n            },\n            {\n                type: \"CNAME\",\n                name: \"autodiscover\",\n                content: \"autodiscover.outlook.com\",\n                ttl: 14400\n            }\n        ]\n    },\n    Cloudflare: {\n        description: \"Basic Cloudflare setup\",\n        records: [\n            {\n                type: \"A\",\n                name: \"@\",\n                content: \"*********\",\n                ttl: 300\n            },\n            {\n                type: \"A\",\n                name: \"www\",\n                content: \"*********\",\n                ttl: 300\n            }\n        ]\n    }\n};\n// Validation helper functions\nconst validateDnsRecord = (type, field, value)=>{\n    const recordType = DNS_RECORD_TYPES[type];\n    if (!recordType || !recordType.validation || !recordType.validation[field]) {\n        return {\n            isValid: true\n        };\n    }\n    const regex = recordType.validation[field];\n    const isValid = regex.test(value);\n    return {\n        isValid,\n        message: isValid ? null : getValidationMessage(type, field)\n    };\n};\nconst getValidationMessage = (type, field)=>{\n    var _messages_type;\n    const messages = {\n        A: {\n            content: \"Please enter a valid IPv4 address (e.g., ***********)\"\n        },\n        AAAA: {\n            content: \"Please enter a valid IPv6 address (e.g., 2001:0db8:85a3::8a2e:0370:7334)\"\n        },\n        CNAME: {\n            content: \"Please enter a valid domain name (e.g., example.com)\"\n        },\n        MX: {\n            content: \"Please enter a valid mail server domain (e.g., mail.example.com)\",\n            priority: \"Priority must be a number between 0 and 65535\"\n        },\n        TXT: {\n            content: \"Text content cannot be empty and must be less than 255 characters\"\n        },\n        NS: {\n            content: \"Please enter a valid nameserver domain (e.g., ns1.example.com)\"\n        },\n        SRV: {\n            name: \"Service name must be in format _service._protocol (e.g., _sip._tcp)\",\n            content: \"Please enter a valid target hostname (e.g., target.example.com)\",\n            priority: \"Priority must be a number between 0 and 65535\",\n            weight: \"Weight must be a number between 0 and 65535\",\n            port: \"Port must be a number between 1 and 65535\"\n        }\n    };\n    return ((_messages_type = messages[type]) === null || _messages_type === void 0 ? void 0 : _messages_type[field]) || \"Invalid value\";\n};\n// Check if a record type has restrictions for certain names\nconst checkRecordRestrictions = (type, name)=>{\n    const recordType = DNS_RECORD_TYPES[type];\n    if (!(recordType === null || recordType === void 0 ? void 0 : recordType.restrictions)) {\n        return {\n            isValid: true\n        };\n    }\n    const { nameCannotBe, message } = recordType.restrictions;\n    const isValid = !nameCannotBe.includes(name);\n    return {\n        isValid,\n        message: isValid ? null : message\n    };\n};\n// Get all available DNS record types as array\nconst getDnsRecordTypes = ()=>{\n    return Object.keys(DNS_RECORD_TYPES).map((key)=>({\n            value: key,\n            label: DNS_RECORD_TYPES[key].label,\n            description: DNS_RECORD_TYPES[key].description\n        }));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/constants/dnsRecords.js\n"));

/***/ })

});
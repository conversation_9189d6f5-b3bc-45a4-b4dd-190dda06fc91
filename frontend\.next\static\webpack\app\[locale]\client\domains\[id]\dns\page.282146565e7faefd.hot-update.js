"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _DnsRecordForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DnsRecordForm */ \"(app-pages-browser)/./src/components/domains/DnsRecordForm.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Don't automatically set DNS service as active just because records loaded\n                // The activation status should come from the domain data, not from successful record loading\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // Don't change DNS service activation status based on record loading failure\n            // The activation status should only be determined by domain.dnsActivated\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load DNS records. Please check if DNS service is activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            console.log(\"\\uD83D\\uDD0D Domain object:\", domain);\n            // The backend expects orderId, which should be the domain's order ID\n            const orderId = domain.orderid || domain.domainOrderId || domain.id;\n            console.log(\"\\uD83D\\uDCCB Using order ID: \".concat(orderId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].activateDnsService(orderId);\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"DNS service activated successfully!\");\n                // Update the domain object with DNS activation status\n                if (onUpdate) {\n                    onUpdate({\n                        dnsActivated: true,\n                        dnsActivatedAt: new Date().toISOString(),\n                        dnsZoneId: response.data.zoneId || null\n                    });\n                }\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].updateDnsRecord(domain.id, editingRecord.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                setEditingRecord(null);\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error updating DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to update DNS record\");\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async function(recordId) {\n        let record = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId), record);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].deleteDnsRecord(domain.id, recordId, record);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type (filter out empty records)\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type && record.content && record.content.trim() !== \"\");\n    };\n    // Update DNS service status when domain changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDnsServiceActive((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.dnsActivated\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-5 w-5 text-amber-600 flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-amber-800\",\n                    children: \"Domain information is required to manage DNS records.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 240,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-gray-900\",\n                                    children: \"DNS Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Manage DNS records for\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: dnsServiceActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-600 font-medium\",\n                                                        children: \"DNS Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 text-amber-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-amber-600 font-medium\",\n                                                        children: \"DNS Inactive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadDnsRecords,\n                                disabled: loading,\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-amber-50 border border-amber-200 rounded-lg p-6 mx-6 mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-amber-900 mb-1\",\n                                            children: \"DNS Service Not Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-amber-800 text-sm leading-relaxed\",\n                                            children: \"You need to activate DNS service before you can manage DNS records for this domain. Once activated, you'll be able to add, edit, and delete DNS records.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: activateDnsService,\n                            disabled: activatingService,\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0\",\n                            children: [\n                                activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 17\n                                }, this),\n                                \"Activate DNS\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-6\",\n                children: [\n                    !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 h-20 bg-amber-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-10 w-10 text-amber-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-medium text-gray-900 mb-3\",\n                                                children: \"DNS Management Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"DNS service is not activated for this domain. Please activate DNS service using the button above to start managing your DNS records.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 327,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this),\n                    dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8 px-6\",\n                                    \"aria-label\": \"Tabs\",\n                                    children: recordTypes.map((param)=>{\n                                        let { type, label } = param;\n                                        const count = getRecordsForType(type).length;\n                                        const isActive = activeTab === type;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(type),\n                                            className: \"\".concat(isActive ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\", \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 23\n                                                }, this),\n                                                count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(isActive ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, type, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    records: getRecordsForType(activeTab),\n                                    recordType: activeTab,\n                                    onEdit: setEditingRecord,\n                                    onDelete: handleDeleteRecord,\n                                    onAdd: handleAddRecord,\n                                    domain: domain,\n                                    loading: loading\n                                }, activeTab, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, this),\n            dnsServiceActive && editingRecord && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: !!editingRecord,\n                onClose: ()=>{\n                    setEditingRecord(null);\n                    setSelectedRecordType(\"A\");\n                },\n                onSubmit: handleEditRecord,\n                initialData: editingRecord,\n                domain: domain,\n                selectedType: selectedRecordType,\n                onTypeChange: setSelectedRecordType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 399,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"TYCtDhHyaCEqeegSAg1NZqzqR7c=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ })

});
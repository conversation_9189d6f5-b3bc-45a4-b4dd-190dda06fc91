"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Don't automatically set DNS service as active just because records loaded\n                // The activation status should come from the domain data, not from successful record loading\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // Don't change DNS service activation status based on record loading failure\n            // The activation status should only be determined by domain.dnsActivated\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load DNS records. Please check if DNS service is activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            console.log(\"\\uD83D\\uDD0D Domain object:\", domain);\n            // The backend expects orderId, which should be the domain's order ID\n            const orderId = domain.orderid || domain.domainOrderId || domain.id;\n            console.log(\"\\uD83D\\uDCCB Using order ID: \".concat(orderId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].activateDnsService(orderId);\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"DNS service activated successfully!\");\n                // Update the domain object with DNS activation status\n                if (onUpdate) {\n                    onUpdate({\n                        dnsActivated: true,\n                        dnsActivatedAt: new Date().toISOString(),\n                        dnsZoneId: response.data.zoneId || null\n                    });\n                }\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].updateDnsRecord(domain.id, recordData.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error updating DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to update DNS record\");\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async function(recordId) {\n        let record = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId), record);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].deleteDnsRecord(domain.id, recordId, record);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type (filter out empty records)\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type && record.content && record.content.trim() !== \"\");\n    };\n    // Update DNS service status when domain changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDnsServiceActive((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.dnsActivated\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-center gap-3\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5 text-amber-600 flex-shrink-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-gray-900\",\n                                    children: \"DNS Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Manage DNS records for\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: dnsServiceActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-600 font-medium\",\n                                                        children: \"DNS Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-amber-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-amber-600 font-medium\",\n                                                        children: \"DNS Inactive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadDnsRecords,\n                                disabled: loading,\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-amber-50 border border-amber-200 rounded-lg p-6 mx-6 mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-amber-900 mb-1\",\n                                            children: \"DNS Service Not Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-amber-800 text-sm leading-relaxed\",\n                                            children: \"You need to activate DNS service before you can manage DNS records for this domain. Once activated, you'll be able to add, edit, and delete DNS records.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: activateDnsService,\n                            disabled: activatingService,\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0\",\n                            children: [\n                                activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 17\n                                }, this),\n                                \"Activate DNS\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 290,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-6\",\n                children: [\n                    !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 h-20 bg-amber-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-10 w-10 text-amber-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-medium text-gray-900 mb-3\",\n                                                children: \"DNS Management Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"DNS service is not activated for this domain. Please activate DNS service using the button above to start managing your DNS records.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this),\n                    dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8 px-6\",\n                                    \"aria-label\": \"Tabs\",\n                                    children: recordTypes.map((param)=>{\n                                        let { type, label } = param;\n                                        const count = getRecordsForType(type).length;\n                                        const isActive = activeTab === type;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(type),\n                                            className: \"\".concat(isActive ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\", \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 23\n                                                }, this),\n                                                count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(isActive ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, type, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    records: getRecordsForType(activeTab),\n                                    recordType: activeTab,\n                                    onEdit: handleEditRecord,\n                                    onDelete: handleDeleteRecord,\n                                    onAdd: handleAddRecord,\n                                    domain: domain,\n                                    loading: loading\n                                }, activeTab, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"L+6j1R40H8OZfVr1iw9tlzSYO8c=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js":
/*!**********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-check-big.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ CircleCheckBig; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21.801 10A10 10 0 1 1 17 3.335\",\n            key: \"yps3ct\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n];\nconst CircleCheckBig = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CircleCheckBig\", __iconNode);\n //# sourceMappingURL=circle-check-big.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2lyY2xlLWNoZWNrLWJpZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVxRDtBQUV0RCxNQUFNQyxhQUFhO0lBQ2pCO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQW1DQyxLQUFLO1FBQVM7S0FBRTtJQUNqRTtRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUFrQkMsS0FBSztRQUFTO0tBQUU7Q0FDakQ7QUFDRCxNQUFNQyxpQkFBaUJKLGdFQUFnQkEsQ0FBQyxrQkFBa0JDO0FBRVQsQ0FDakQsNENBQTRDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2lyY2xlLWNoZWNrLWJpZy5qcz8zZDFiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjQ3NS4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgX19pY29uTm9kZSA9IFtcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTIxLjgwMSAxMEExMCAxMCAwIDEgMSAxNyAzLjMzNVwiLCBrZXk6IFwieXBzM2N0XCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIm05IDExIDMgM0wyMiA0XCIsIGtleTogXCIxcGZsemxcIiB9XVxuXTtcbmNvbnN0IENpcmNsZUNoZWNrQmlnID0gY3JlYXRlTHVjaWRlSWNvbihcIkNpcmNsZUNoZWNrQmlnXCIsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgeyBfX2ljb25Ob2RlLCBDaXJjbGVDaGVja0JpZyBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jaXJjbGUtY2hlY2stYmlnLmpzLm1hcFxuIl0sIm5hbWVzIjpbImNyZWF0ZUx1Y2lkZUljb24iLCJfX2ljb25Ob2RlIiwiZCIsImtleSIsIkNpcmNsZUNoZWNrQmlnIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/refresh-cw.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ RefreshCw; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\",\n            key: \"v9h5vc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 3v5h-5\",\n            key: \"1q7to0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\",\n            key: \"3uifl3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 16H3v5\",\n            key: \"1cv678\"\n        }\n    ]\n];\nconst RefreshCw = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"RefreshCw\", __iconNode);\n //# sourceMappingURL=refresh-cw.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcmVmcmVzaC1jdy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVxRDtBQUV0RCxNQUFNQyxhQUFhO0lBQ2pCO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQXNEQyxLQUFLO1FBQVM7S0FBRTtJQUNwRjtRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUFjQyxLQUFLO1FBQVM7S0FBRTtJQUM1QztRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUF1REMsS0FBSztRQUFTO0tBQUU7SUFDckY7UUFBQztRQUFRO1lBQUVELEdBQUc7WUFBYUMsS0FBSztRQUFTO0tBQUU7Q0FDNUM7QUFDRCxNQUFNQyxZQUFZSixnRUFBZ0JBLENBQUMsYUFBYUM7QUFFSixDQUM1QyxzQ0FBc0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9yZWZyZXNoLWN3LmpzPzIwZWEiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNDc1LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBfX2ljb25Ob2RlID0gW1xuICBbXCJwYXRoXCIsIHsgZDogXCJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOFwiLCBrZXk6IFwidjloNXZjXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0yMSAzdjVoLTVcIiwga2V5OiBcIjFxN3RvMFwiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNMjEgMTJhOSA5IDAgMCAxLTkgOSA5Ljc1IDkuNzUgMCAwIDEtNi43NC0yLjc0TDMgMTZcIiwga2V5OiBcIjN1aWZsM1wiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNOCAxNkgzdjVcIiwga2V5OiBcIjFjdjY3OFwiIH1dXG5dO1xuY29uc3QgUmVmcmVzaEN3ID0gY3JlYXRlTHVjaWRlSWNvbihcIlJlZnJlc2hDd1wiLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IHsgX19pY29uTm9kZSwgUmVmcmVzaEN3IGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlZnJlc2gtY3cuanMubWFwXG4iXSwibmFtZXMiOlsiY3JlYXRlTHVjaWRlSWNvbiIsIl9faWNvbk5vZGUiLCJkIiwia2V5IiwiUmVmcmVzaEN3IiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Don't automatically set DNS service as active just because records loaded\n                // The activation status should come from the domain data, not from successful record loading\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // Don't change DNS service activation status based on record loading failure\n            // The activation status should only be determined by domain.dnsActivated\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load DNS records. Please check if DNS service is activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            console.log(\"\\uD83D\\uDD0D Domain object:\", domain);\n            // The backend expects orderId, which should be the domain's order ID\n            const orderId = domain.orderid || domain.domainOrderId || domain.id;\n            console.log(\"\\uD83D\\uDCCB Using order ID: \".concat(orderId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].activateDnsService(orderId);\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"DNS service activated successfully!\");\n                // Update the domain object with DNS activation status\n                if (onUpdate) {\n                    onUpdate({\n                        dnsActivated: true,\n                        dnsActivatedAt: new Date().toISOString(),\n                        dnsZoneId: response.data.zoneId || null\n                    });\n                }\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].updateDnsRecord(domain.id, recordData.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            var _error_response, _error_response_data, _error_response1;\n            console.error(\"❌ Error updating DNS record:\", error);\n            // Handle locked records specifically\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 423 || ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.locked)) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"\\uD83D\\uDD12 This DNS record is locked by the registrar and cannot be modified. Default nameserver records are typically locked to prevent accidental deletion of critical DNS infrastructure.\", {\n                    autoClose: 8000\n                });\n            } else {\n                var _error_response_data1, _error_response2, _error_response_data2, _error_response3;\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data1 = _error_response2.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || ((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : (_error_response_data2 = _error_response3.data) === null || _error_response_data2 === void 0 ? void 0 : _error_response_data2.error) || \"Failed to update DNS record\");\n            }\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async function(recordId) {\n        let record = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId), record);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].deleteDnsRecord(domain.id, recordId, record);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type (filter out empty records)\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type && record.content && record.content.trim() !== \"\");\n    };\n    // Update DNS service status when domain changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDnsServiceActive((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.dnsActivated\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-center gap-3\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5 text-amber-600 flex-shrink-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 252,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-gray-900\",\n                                    children: \"DNS Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Manage DNS records for\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: dnsServiceActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-600 font-medium\",\n                                                        children: \"DNS Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-amber-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-amber-600 font-medium\",\n                                                        children: \"DNS Inactive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadDnsRecords,\n                                disabled: loading,\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 262,\n                columnNumber: 7\n            }, this),\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-amber-50 border border-amber-200 rounded-lg p-6 mx-6 mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-amber-900 mb-1\",\n                                            children: \"DNS Service Not Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-amber-800 text-sm leading-relaxed\",\n                                            children: \"You need to activate DNS service before you can manage DNS records for this domain. Once activated, you'll be able to add, edit, and delete DNS records.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: activateDnsService,\n                            disabled: activatingService,\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0\",\n                            children: [\n                                activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 17\n                                }, this),\n                                \"Activate DNS\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 316,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 303,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-6\",\n                children: [\n                    !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 h-20 bg-amber-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-10 w-10 text-amber-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-medium text-gray-900 mb-3\",\n                                                children: \"DNS Management Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"DNS service is not activated for this domain. Please activate DNS service using the button above to start managing your DNS records.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 339,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, this),\n                    dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8 px-6\",\n                                    \"aria-label\": \"Tabs\",\n                                    children: recordTypes.map((param)=>{\n                                        let { type, label } = param;\n                                        const count = getRecordsForType(type).length;\n                                        const isActive = activeTab === type;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(type),\n                                            className: \"\".concat(isActive ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\", \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 23\n                                                }, this),\n                                                count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(isActive ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, type, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    records: getRecordsForType(activeTab),\n                                    recordType: activeTab,\n                                    onEdit: handleEditRecord,\n                                    onDelete: handleDeleteRecord,\n                                    onAdd: handleAddRecord,\n                                    domain: domain,\n                                    loading: loading\n                                }, activeTab, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 334,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 260,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"L+6j1R40H8OZfVr1iw9tlzSYO8c=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx":
/*!***************************************************!*\
  !*** ./src/components/domains/DnsRecordTable.jsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DnsRecordTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/dnsRecords */ \"(app-pages-browser)/./src/constants/dnsRecords.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DnsRecordTable(param) {\n    let { records, recordType, onEdit, onDelete, onAdd, domain, loading = false } = param;\n    _s();\n    const [deleteConfirm, setDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInlineForm, setShowInlineForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Inline editing state\n    const [editingRecordId, setEditingRecordId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editFormData, setEditFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editFormErrors, setEditFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isEditSubmitting, setIsEditSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Reset form state when record type changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowInlineForm(false);\n        setFormData({});\n        setFormErrors({});\n    }, [\n        recordType\n    ]);\n    // Initialize form data for the current record type\n    const initializeFormData = (type)=>{\n        const recordTypeConfig = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DNS_RECORD_TYPES[type];\n        if (!recordTypeConfig) return {\n            type\n        };\n        const initialData = {\n            type\n        };\n        recordTypeConfig.fields.forEach((field)=>{\n            if (field.name === \"ttl\") {\n                initialData[field.name] = field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL;\n            } else if (field.type === \"number\") {\n                initialData[field.name] = field.default || 0;\n            } else {\n                initialData[field.name] = field.default || \"\";\n            }\n        });\n        return initialData;\n    };\n    // Handle add button click - shows inline form\n    const handleAddClick = ()=>{\n        const initialData = initializeFormData(recordType);\n        setFormData(initialData);\n        setFormErrors({});\n        setShowInlineForm(true);\n    };\n    // Handle form field changes\n    const handleFieldChange = (fieldName, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [fieldName]: value\n            }));\n        // Clear error for this field\n        if (formErrors[fieldName]) {\n            setFormErrors((prev)=>({\n                    ...prev,\n                    [fieldName]: null\n                }));\n        }\n    };\n    // Handle form submission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            // Validate the form data\n            const validation = (0,_constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.validateDnsRecord)(formData);\n            if (!validation.isValid) {\n                setFormErrors(validation.errors);\n                return;\n            }\n            await onAdd(formData);\n            setShowInlineForm(false);\n            setFormData({});\n            setFormErrors({});\n        } catch (error) {\n            console.error(\"Error adding record:\", error);\n        // Handle validation errors if needed\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Handle form cancel\n    const handleCancel = ()=>{\n        setShowInlineForm(false);\n        setFormData({});\n        setFormErrors({});\n    };\n    // Handle edit button click - shows inline edit form\n    const handleEditClick = (record)=>{\n        setEditingRecordId(record.id);\n        setEditFormData({\n            ...record\n        });\n        setEditFormErrors({});\n        setShowInlineForm(false); // Hide add form if open\n    };\n    // Handle edit form field changes\n    const handleEditFieldChange = (fieldName, value)=>{\n        setEditFormData((prev)=>({\n                ...prev,\n                [fieldName]: value\n            }));\n        // Clear error for this field\n        if (editFormErrors[fieldName]) {\n            setEditFormErrors((prev)=>({\n                    ...prev,\n                    [fieldName]: undefined\n                }));\n        }\n    };\n    // Handle edit form submission\n    const handleEditSubmit = async (e)=>{\n        e.preventDefault();\n        setIsEditSubmitting(true);\n        try {\n            // Validate the form data\n            const validation = (0,_constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.validateDnsRecord)(editFormData);\n            if (!validation.isValid) {\n                setEditFormErrors(validation.errors);\n                return;\n            }\n            await onEdit(editFormData);\n            setEditingRecordId(null);\n            setEditFormData({});\n            setEditFormErrors({});\n        } catch (error) {\n            console.error(\"Error editing record:\", error);\n        // Handle validation errors if needed\n        } finally{\n            setIsEditSubmitting(false);\n        }\n    };\n    // Handle edit form cancel\n    const handleEditCancel = ()=>{\n        setEditingRecordId(null);\n        setEditFormData({});\n        setEditFormErrors({});\n    };\n    // Get record type configuration\n    const getRecordTypeConfig = (type)=>{\n        const configs = {\n            A: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                label: \"IPv4 Address\",\n                color: \"blue\",\n                bgColor: \"bg-blue-50\",\n                textColor: \"text-blue-600\",\n                borderColor: \"border-blue-200\"\n            },\n            AAAA: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                label: \"IPv6 Address\",\n                color: \"purple\",\n                bgColor: \"bg-purple-50\",\n                textColor: \"text-purple-600\",\n                borderColor: \"border-purple-200\"\n            },\n            CNAME: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                label: \"Canonical Name\",\n                color: \"green\",\n                bgColor: \"bg-green-50\",\n                textColor: \"text-green-600\",\n                borderColor: \"border-green-200\"\n            },\n            MX: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                label: \"Mail Exchange\",\n                color: \"orange\",\n                bgColor: \"bg-orange-50\",\n                textColor: \"text-orange-600\",\n                borderColor: \"border-orange-200\"\n            },\n            TXT: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                label: \"Text Record\",\n                color: \"gray\",\n                bgColor: \"bg-gray-50\",\n                textColor: \"text-gray-600\",\n                borderColor: \"border-gray-200\"\n            },\n            NS: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                label: \"Name Server\",\n                color: \"indigo\",\n                bgColor: \"bg-indigo-50\",\n                textColor: \"text-indigo-600\",\n                borderColor: \"border-indigo-200\"\n            },\n            SRV: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                label: \"Service Record\",\n                color: \"pink\",\n                bgColor: \"bg-pink-50\",\n                textColor: \"text-pink-600\",\n                borderColor: \"border-pink-200\"\n            }\n        };\n        return configs[type] || configs.A;\n    };\n    // Format TTL display\n    const formatTTL = (ttl)=>{\n        const numericTTL = parseInt(ttl);\n        // Format TTL values in a human-readable way\n        if (numericTTL >= 86400) {\n            const days = Math.floor(numericTTL / 86400);\n            const remainder = numericTTL % 86400;\n            if (remainder === 0) {\n                return \"\".concat(days, \" day\").concat(days !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 3600) {\n            const hours = Math.floor(numericTTL / 3600);\n            const remainder = numericTTL % 3600;\n            if (remainder === 0) {\n                return \"\".concat(hours, \" hour\").concat(hours !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 60) {\n            const minutes = Math.floor(numericTTL / 60);\n            const remainder = numericTTL % 60;\n            if (remainder === 0) {\n                return \"\".concat(minutes, \" minute\").concat(minutes !== 1 ? \"s\" : \"\");\n            }\n        }\n        return \"\".concat(numericTTL, \"s\");\n    };\n    // Format record name\n    const formatRecordName = (name)=>{\n        if (!name || name === \"@\") return (domain === null || domain === void 0 ? void 0 : domain.name) || \"@\";\n        if (name.endsWith(\".\")) return name.slice(0, -1);\n        return name;\n    };\n    // Copy to clipboard\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n        } catch (err) {\n            console.error(\"Failed to copy:\", err);\n        }\n    };\n    // Handle delete confirmation\n    const handleDeleteClick = (record)=>{\n        setDeleteConfirm(record);\n    };\n    const handleDeleteConfirm = ()=>{\n        if (deleteConfirm && onDelete) {\n            onDelete(deleteConfirm.id, deleteConfirm);\n        }\n        setDeleteConfirm(null);\n    };\n    const config = getRecordTypeConfig(recordType);\n    const IconComponent = config.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 rounded-lg \".concat(config.bgColor, \" border border-gray-200\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"h-5 w-5 \".concat(config.textColor)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: [\n                                                    recordType,\n                                                    \" Records\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    config.label,\n                                                    \" • \",\n                                                    records.length,\n                                                    \" record\",\n                                                    records.length !== 1 ? \"s\" : \"\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this),\n                            !showInlineForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAddClick,\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add \",\n                                    recordType\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this),\n                    showInlineForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-medium text-blue-900\",\n                                            children: [\n                                                \"Add New \",\n                                                recordType,\n                                                \" Record\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 15\n                                }, this),\n                                (()=>{\n                                    const recordTypeConfig = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DNS_RECORD_TYPES[recordType];\n                                    if (!recordTypeConfig) return null;\n                                    const fields = recordTypeConfig.fields;\n                                    const gridCols = fields.length <= 3 ? \"md:grid-cols-\".concat(fields.length) : \"md:grid-cols-3\";\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 \".concat(gridCols, \" gap-4\"),\n                                        children: fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: [\n                                                            field.label,\n                                                            field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 ml-1\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 46\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    field.name === \"ttl\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData[field.name] || field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL,\n                                                        onChange: (e)=>handleFieldChange(field.name, parseInt(e.target.value) || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL),\n                                                        placeholder: \"TTL in seconds\",\n                                                        min: \"60\",\n                                                        max: \"2147483647\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 27\n                                                    }, this) : field.type === \"number\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, parseInt(e.target.value) || 0),\n                                                        placeholder: field.placeholder,\n                                                        min: field.min,\n                                                        max: field.max,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 27\n                                                    }, this) : field.type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                                                        placeholder: field.placeholder,\n                                                        rows: 2,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                                                        placeholder: field.placeholder,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    formErrors[field.name] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: formErrors[field.name]\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 19\n                                    }, this);\n                                })(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between pt-4 border-t border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: [\n                                                'Use \"@\" for root domain (',\n                                                domain === null || domain === void 0 ? void 0 : domain.name,\n                                                '), \"www\" for www.',\n                                                domain === null || domain === void 0 ? void 0 : domain.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleCancel,\n                                                    disabled: isSubmitting,\n                                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 inline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Cancel\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isSubmitting,\n                                                    className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"animate-spin h-4 w-4 mr-2 inline\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        className: \"opacity-25\",\n                                                                        cx: \"12\",\n                                                                        cy: \"12\",\n                                                                        r: \"10\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        className: \"opacity-75\",\n                                                                        fill: \"currentColor\",\n                                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Adding...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2 inline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Add Record\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 341,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 340,\n                        columnNumber: 11\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, this),\n                                \"Loading \",\n                                recordType,\n                                \" records...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 454,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 453,\n                        columnNumber: 11\n                    }, this) : records.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 rounded-full \".concat(config.bgColor),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-8 w-8 \".concat(config.textColor)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: [\n                                                \"No \",\n                                                recordType,\n                                                \" records found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: [\n                                                \"Create your first \",\n                                                recordType,\n                                                \" record to get started\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddClick,\n                                            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add \",\n                                                recordType,\n                                                \" Record\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 464,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 463,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"TTL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, this),\n                                            (recordType === \"MX\" || recordType === \"SRV\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Priority\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"relative px-6 py-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 505,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 488,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: records.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50 transition-colors duration-150\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: formatRecordName(record.name)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-mono text-gray-700 flex-1 min-w-0 truncate\",\n                                                                children: record.content\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>copyToClipboard(record.content),\n                                                                className: \"p-1 text-gray-400 hover:text-gray-600 rounded\",\n                                                                title: \"Copy to clipboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            formatTTL(record.ttl)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 21\n                                                }, this),\n                                                (recordType === \"MX\" || recordType === \"SRV\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-900\",\n                                                        children: record.priority || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleEditClick(record),\n                                                                className: \"p-2 text-gray-400 hover:text-blue-600 rounded-md hover:bg-blue-50\",\n                                                                title: \"Edit record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDeleteClick(record),\n                                                                className: \"p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-red-50\",\n                                                                title: \"Delete record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 558,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, record.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 487,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 486,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 311,\n                columnNumber: 7\n            }, this),\n            deleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 591,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center sm:text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg leading-6 font-medium text-gray-900\",\n                                            children: \"Delete DNS Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 589,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mb-4\",\n                                        children: [\n                                            \"Are you sure you want to delete this \",\n                                            deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.type,\n                                            \" record? This action cannot be undone.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-gray-50 rounded-lg border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                    children: formatRecordName(deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.name)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-mono text-gray-600\",\n                                                    children: deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.content\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 599,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDeleteConfirm(null),\n                                        className: \"flex-1 px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 615,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDeleteConfirm,\n                                        className: \"flex-1 px-4 py-2 bg-red-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                        children: \"Delete Record\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 614,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 588,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                    lineNumber: 587,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 586,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DnsRecordTable, \"jg6ldIq40+iCHRKQQt63Kt0bQRg=\");\n_c = DnsRecordTable;\nvar _c;\n$RefreshReg$(_c, \"DnsRecordTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\n"));

/***/ })

});
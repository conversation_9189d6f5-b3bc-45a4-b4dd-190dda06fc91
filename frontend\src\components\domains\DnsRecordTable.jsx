"use client";
import React, { useState, useEffect } from "react";
import {
  Globe,
  Link,
  Mail,
  FileText,
  Server,
  Settings,
  Edit,
  Trash2,
  Copy,
  Plus,
  AlertTriangle,
  Clock,
  Save,
  X,
  Info,
} from "lucide-react";
import {
  DNS_RECORD_TYPES,
  DEFAULT_TTL,
  validateDnsRecord
} from "@/constants/dnsRecords";

export default function DnsRecordTable({
  records,
  recordType,
  onEdit,
  onDelete,
  onAdd,
  domain,
  loading = false,
}) {
  const [deleteConfirm, setDeleteConfirm] = useState(null);
  const [showInlineForm, setShowInlineForm] = useState(false);
  const [formData, setFormData] = useState({});
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Inline editing state
  const [editingRecordId, setEditingRecordId] = useState(null);
  const [editFormData, setEditFormData] = useState({});
  const [editFormErrors, setEditFormErrors] = useState({});
  const [isEditSubmitting, setIsEditSubmitting] = useState(false);

  // Reset form state when record type changes
  useEffect(() => {
    setShowInlineForm(false);
    setFormData({});
    setFormErrors({});
  }, [recordType]);

  // Initialize form data for the current record type
  const initializeFormData = (type) => {
    const recordTypeConfig = DNS_RECORD_TYPES[type];
    if (!recordTypeConfig) return { type };

    const initialData = { type };
    recordTypeConfig.fields.forEach(field => {
      if (field.name === 'ttl') {
        initialData[field.name] = field.default || DEFAULT_TTL;
      } else if (field.type === 'number') {
        initialData[field.name] = field.default || 0;
      } else {
        initialData[field.name] = field.default || '';
      }
    });

    return initialData;
  };

  // Handle add button click - shows inline form
  const handleAddClick = () => {
    const initialData = initializeFormData(recordType);
    setFormData(initialData);
    setFormErrors({});
    setShowInlineForm(true);
  };

  // Handle form field changes
  const handleFieldChange = (fieldName, value) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));

    // Clear error for this field
    if (formErrors[fieldName]) {
      setFormErrors(prev => ({
        ...prev,
        [fieldName]: null
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate the form data
      const validation = validateDnsRecord(formData);
      if (!validation.isValid) {
        setFormErrors(validation.errors);
        return;
      }

      await onAdd(formData);
      setShowInlineForm(false);
      setFormData({});
      setFormErrors({});
    } catch (error) {
      console.error('Error adding record:', error);
      // Handle validation errors if needed
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form cancel
  const handleCancel = () => {
    setShowInlineForm(false);
    setFormData({});
    setFormErrors({});
  };

  // Handle edit button click - shows inline edit form
  const handleEditClick = (record) => {
    setEditingRecordId(record.id);
    setEditFormData({
      ...record,
      originalContent: record.content // Store original content for API
    });
    setEditFormErrors({});
    setShowInlineForm(false); // Hide add form if open
  };

  // Handle edit form field changes
  const handleEditFieldChange = (fieldName, value) => {
    setEditFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));

    // Clear error for this field
    if (editFormErrors[fieldName]) {
      setEditFormErrors(prev => ({
        ...prev,
        [fieldName]: undefined
      }));
    }
  };

  // Handle edit form submission
  const handleEditSubmit = async (e) => {
    e.preventDefault();
    setIsEditSubmitting(true);

    try {
      // Validate the form data
      const validation = validateDnsRecord(editFormData);
      if (!validation.isValid) {
        setEditFormErrors(validation.errors);
        return;
      }

      await onEdit(editFormData);
      setEditingRecordId(null);
      setEditFormData({});
      setEditFormErrors({});
    } catch (error) {
      console.error('Error editing record:', error);
      // Handle validation errors if needed
    } finally {
      setIsEditSubmitting(false);
    }
  };

  // Handle edit form cancel
  const handleEditCancel = () => {
    setEditingRecordId(null);
    setEditFormData({});
    setEditFormErrors({});
  };



  // Get record type configuration
  const getRecordTypeConfig = (type) => {
    const configs = {
      A: {
        icon: Globe,
        label: "IPv4 Address",
        color: "blue",
        bgColor: "bg-blue-50",
        textColor: "text-blue-600",
        borderColor: "border-blue-200",
      },
      AAAA: {
        icon: Globe,
        label: "IPv6 Address",
        color: "purple",
        bgColor: "bg-purple-50",
        textColor: "text-purple-600",
        borderColor: "border-purple-200",
      },
      CNAME: {
        icon: Link,
        label: "Canonical Name",
        color: "green",
        bgColor: "bg-green-50",
        textColor: "text-green-600",
        borderColor: "border-green-200",
      },
      MX: {
        icon: Mail,
        label: "Mail Exchange",
        color: "orange",
        bgColor: "bg-orange-50",
        textColor: "text-orange-600",
        borderColor: "border-orange-200",
      },
      TXT: {
        icon: FileText,
        label: "Text Record",
        color: "gray",
        bgColor: "bg-gray-50",
        textColor: "text-gray-600",
        borderColor: "border-gray-200",
      },
      NS: {
        icon: Server,
        label: "Name Server",
        color: "indigo",
        bgColor: "bg-indigo-50",
        textColor: "text-indigo-600",
        borderColor: "border-indigo-200",
      },
      SRV: {
        icon: Settings,
        label: "Service Record",
        color: "pink",
        bgColor: "bg-pink-50",
        textColor: "text-pink-600",
        borderColor: "border-pink-200",
      },
    };
    return configs[type] || configs.A;
  };

  // Format TTL display
  const formatTTL = (ttl) => {
    const numericTTL = parseInt(ttl);

    // Format TTL values in a human-readable way
    if (numericTTL >= 86400) {
      const days = Math.floor(numericTTL / 86400);
      const remainder = numericTTL % 86400;
      if (remainder === 0) {
        return `${days} day${days !== 1 ? 's' : ''}`;
      }
    } else if (numericTTL >= 3600) {
      const hours = Math.floor(numericTTL / 3600);
      const remainder = numericTTL % 3600;
      if (remainder === 0) {
        return `${hours} hour${hours !== 1 ? 's' : ''}`;
      }
    } else if (numericTTL >= 60) {
      const minutes = Math.floor(numericTTL / 60);
      const remainder = numericTTL % 60;
      if (remainder === 0) {
        return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
      }
    }

    return `${numericTTL}s`;
  };

  // Format record name
  const formatRecordName = (name) => {
    if (!name || name === "@") return domain?.name || "@";
    if (name.endsWith(".")) return name.slice(0, -1);
    return name;
  };

  // Copy to clipboard
  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  };

  // Handle delete confirmation
  const handleDeleteClick = (record) => {
    setDeleteConfirm(record);
  };

  const handleDeleteConfirm = () => {
    if (deleteConfirm && onDelete) {
      onDelete(deleteConfirm.id, deleteConfirm);
    }
    setDeleteConfirm(null);
  };

  const config = getRecordTypeConfig(recordType);
  const IconComponent = config.icon;

  return (
    <>
      <div className="w-full">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${config.bgColor} border border-gray-200`}>
              <IconComponent className={`h-5 w-5 ${config.textColor}`} />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {recordType} Records
              </h3>
              <p className="text-sm text-gray-600">
                {config.label} • {records.length} record{records.length !== 1 ? 's' : ''}
              </p>
            </div>
          </div>
          {!showInlineForm && (
            <button
              onClick={handleAddClick}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add {recordType}
            </button>
          )}
        </div>

        {/* Inline Add Form */}
        {showInlineForm && (
          <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <Info className="h-5 w-5 text-blue-600" />
                <h4 className="text-lg font-medium text-blue-900">
                  Add New {recordType} Record
                </h4>
              </div>

              {/* Dynamic Fields based on record type */}
              {(() => {
                const recordTypeConfig = DNS_RECORD_TYPES[recordType];
                if (!recordTypeConfig) return null;

                const fields = recordTypeConfig.fields;
                const gridCols = fields.length <= 3 ? `md:grid-cols-${fields.length}` : 'md:grid-cols-3';

                return (
                  <div className={`grid grid-cols-1 ${gridCols} gap-4`}>
                    {fields.map((field) => (
                      <div key={field.name}>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {field.label}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </label>
                        {field.name === 'ttl' ? (
                          <input
                            type="number"
                            value={formData[field.name] || field.default || DEFAULT_TTL}
                            onChange={(e) => handleFieldChange(field.name, parseInt(e.target.value) || DEFAULT_TTL)}
                            placeholder="TTL in seconds"
                            min="60"
                            max="2147483647"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        ) : field.type === 'number' ? (
                          <input
                            type="number"
                            value={formData[field.name] || ''}
                            onChange={(e) => handleFieldChange(field.name, parseInt(e.target.value) || 0)}
                            placeholder={field.placeholder}
                            min={field.min}
                            max={field.max}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        ) : field.type === 'textarea' ? (
                          <textarea
                            value={formData[field.name] || ''}
                            onChange={(e) => handleFieldChange(field.name, e.target.value)}
                            placeholder={field.placeholder}
                            rows={2}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        ) : (
                          <input
                            type="text"
                            value={formData[field.name] || ''}
                            onChange={(e) => handleFieldChange(field.name, e.target.value)}
                            placeholder={field.placeholder}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        )}
                        {formErrors[field.name] && (
                          <p className="mt-1 text-sm text-red-600">{formErrors[field.name]}</p>
                        )}
                      </div>
                    ))}
                  </div>
                );
              })()}

              <div className="flex items-center justify-between pt-4 border-t border-blue-200">
                <div className="text-sm text-blue-700">
                  Use "@" for root domain ({domain?.name}), "www" for www.{domain?.name}
                </div>
                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={handleCancel}
                    disabled={isSubmitting}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    <X className="h-4 w-4 mr-2 inline" />
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Adding...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2 inline" />
                        Add Record
                      </>
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        )}

        {/* Table Content */}
        {loading ? (
          <div className="text-center py-12">
            <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading {recordType} records...
            </div>
          </div>
        ) : records.length === 0 ? (
          <div className="text-center py-16">
            <div className="flex flex-col items-center gap-4">
              <div className={`p-4 rounded-full ${config.bgColor}`}>
                <IconComponent className={`h-8 w-8 ${config.textColor}`} />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No {recordType} records found
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  Create your first {recordType} record to get started
                </p>
                <button
                  onClick={handleAddClick}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add {recordType} Record
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
            <table className="min-w-full divide-y divide-gray-300">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Content
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    TTL
                  </th>
                  {(recordType === 'MX' || recordType === 'SRV') && (
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Priority
                    </th>
                  )}
                  <th scope="col" className="relative px-6 py-3">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">


                {records.map((record) => (
                  <React.Fragment key={record.id}>
                    {/* Normal table row */}
                    {editingRecordId !== record.id && (
                      <tr className="hover:bg-gray-50 transition-colors duration-150">
                        {/* Name Column */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {formatRecordName(record.name)}
                          </div>
                        </td>

                        {/* Content Column */}
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            <div className="text-sm font-mono text-gray-700 flex-1 min-w-0 truncate">
                              {record.content}
                            </div>
                            <button
                              onClick={() => copyToClipboard(record.content)}
                              className="p-1 text-gray-400 hover:text-gray-600 rounded"
                              title="Copy to clipboard"
                            >
                              <Copy className="h-4 w-4" />
                            </button>
                          </div>
                        </td>

                        {/* TTL Column */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <Clock className="h-3 w-3 mr-1" />
                            {formatTTL(record.ttl)}
                          </span>
                        </td>

                        {/* Priority Column (for MX and SRV records) */}
                        {(recordType === 'MX' || recordType === 'SRV') && (
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {record.priority || '-'}
                            </div>
                          </td>
                        )}

                        {/* Actions Column */}
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end gap-2">
                            <button
                              onClick={() => handleEditClick(record)}
                              className="p-2 text-gray-400 hover:text-blue-600 rounded-md hover:bg-blue-50"
                              title="Edit record"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteClick(record)}
                              className="p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-red-50"
                              title="Delete record"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    )}

                    {/* Inline edit form row */}
                    {editingRecordId === record.id && (
                      <tr className="bg-blue-50">
                        <td colSpan={recordType === 'MX' || recordType === 'SRV' ? 5 : 4} className="px-6 py-6">
                          <form onSubmit={handleEditSubmit} className="space-y-4">
                            <div className="flex items-center gap-2 mb-4">
                              <Edit className="h-5 w-5 text-blue-600" />
                              <h4 className="text-lg font-medium text-blue-900">
                                Edit {recordType} Record
                              </h4>
                            </div>

                            {/* Dynamic Fields based on record type */}
                            {(() => {
                              const recordTypeConfig = DNS_RECORD_TYPES[recordType];
                              if (!recordTypeConfig) return null;

                              const fields = recordTypeConfig.fields;

                              return (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                  {fields.map((field) => (
                                    <div key={field.name}>
                                      <label className="block text-sm font-medium text-gray-700 mb-1">
                                        {field.label}
                                        {field.required && <span className="text-red-500 ml-1">*</span>}
                                      </label>
                                      {field.name === 'ttl' ? (
                                        <input
                                          type="number"
                                          value={editFormData[field.name] || field.default || DEFAULT_TTL}
                                          onChange={(e) => handleEditFieldChange(field.name, parseInt(e.target.value) || DEFAULT_TTL)}
                                          placeholder="TTL in seconds"
                                          min="60"
                                          max="2147483647"
                                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        />
                                      ) : field.type === 'number' ? (
                                        <input
                                          type="number"
                                          value={editFormData[field.name] || ''}
                                          onChange={(e) => handleEditFieldChange(field.name, parseInt(e.target.value) || 0)}
                                          placeholder={field.placeholder}
                                          min={field.min}
                                          max={field.max}
                                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        />
                                      ) : field.type === 'textarea' ? (
                                        <textarea
                                          value={editFormData[field.name] || ''}
                                          onChange={(e) => handleEditFieldChange(field.name, e.target.value)}
                                          placeholder={field.placeholder}
                                          rows={2}
                                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        />
                                      ) : (
                                        <input
                                          type="text"
                                          value={editFormData[field.name] || ''}
                                          onChange={(e) => handleEditFieldChange(field.name, e.target.value)}
                                          placeholder={field.placeholder}
                                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        />
                                      )}
                                      {editFormErrors[field.name] && (
                                        <p className="mt-1 text-sm text-red-600">{editFormErrors[field.name]}</p>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              );
                            })()}

                            {/* Form Actions */}
                            <div className="flex items-center justify-end gap-3 pt-4 border-t border-blue-200">
                              <button
                                type="button"
                                onClick={handleEditCancel}
                                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                              >
                                Cancel
                              </button>
                              <button
                                type="submit"
                                disabled={isEditSubmitting}
                                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                {isEditSubmitting ? (
                                  <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Updating...
                                  </>
                                ) : (
                                  <>
                                    <Save className="h-4 w-4 mr-2" />
                                    Update Record
                                  </>
                                )}
                              </button>
                            </div>
                          </form>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center gap-3 mb-4">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <div className="text-center sm:text-left">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Delete DNS Record
                  </h3>
                </div>
              </div>
              <div className="mt-2">
                <p className="text-sm text-gray-500 mb-4">
                  Are you sure you want to delete this {deleteConfirm?.type} record? This action cannot be undone.
                </p>
                <div className="p-3 bg-gray-50 rounded-lg border">
                  <div className="text-sm">
                    <div className="font-medium text-gray-900 mb-1">
                      {formatRecordName(deleteConfirm?.name)}
                    </div>
                    <div className="font-mono text-gray-600">
                      {deleteConfirm?.content}
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex gap-3 mt-6">
                <button
                  onClick={() => setDeleteConfirm(null)}
                  className="flex-1 px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteConfirm}
                  className="flex-1 px-4 py-2 bg-red-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Delete Record
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Don't automatically set DNS service as active just because records loaded\n                // The activation status should come from the domain data, not from successful record loading\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // Don't change DNS service activation status based on record loading failure\n            // The activation status should only be determined by domain.dnsActivated\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load DNS records. Please check if DNS service is activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            console.log(\"\\uD83D\\uDD0D Domain object:\", domain);\n            // The backend expects orderId, which should be the domain's order ID\n            const orderId = domain.orderid || domain.domainOrderId || domain.id;\n            console.log(\"\\uD83D\\uDCCB Using order ID: \".concat(orderId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].activateDnsService(orderId);\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"DNS service activated successfully!\");\n                // Update the domain object with DNS activation status\n                if (onUpdate) {\n                    onUpdate({\n                        dnsActivated: true,\n                        dnsActivatedAt: new Date().toISOString(),\n                        dnsZoneId: response.data.zoneId || null\n                    });\n                }\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].updateDnsRecord(domain.id, recordData.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error updating DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to update DNS record\");\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async function(recordId) {\n        let record = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId), record);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].deleteDnsRecord(domain.id, recordId, record);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type (filter out empty records)\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type && record.content && record.content.trim() !== \"\");\n    };\n    // Update DNS service status when domain changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDnsServiceActive((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.dnsActivated\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5 text-amber-600 flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-amber-800\",\n                    children: \"Domain information is required to manage DNS records.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-gray-900\",\n                                    children: \"DNS Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Manage DNS records for\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: dnsServiceActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-600 font-medium\",\n                                                        children: \"DNS Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-amber-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-amber-600 font-medium\",\n                                                        children: \"DNS Inactive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadDnsRecords,\n                                disabled: loading,\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-amber-50 border border-amber-200 rounded-lg p-6 mx-6 mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-amber-900 mb-1\",\n                                            children: \"DNS Service Not Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-amber-800 text-sm leading-relaxed\",\n                                            children: \"You need to activate DNS service before you can manage DNS records for this domain. Once activated, you'll be able to add, edit, and delete DNS records.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: activateDnsService,\n                            disabled: activatingService,\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0\",\n                            children: [\n                                activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 17\n                                }, this),\n                                \"Activate DNS\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 290,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-6\",\n                children: [\n                    !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 h-20 bg-amber-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-10 w-10 text-amber-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-medium text-gray-900 mb-3\",\n                                                children: \"DNS Management Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"DNS service is not activated for this domain. Please activate DNS service using the button above to start managing your DNS records.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this),\n                    dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8 px-6\",\n                                    \"aria-label\": \"Tabs\",\n                                    children: recordTypes.map((param)=>{\n                                        let { type, label } = param;\n                                        const count = getRecordsForType(type).length;\n                                        const isActive = activeTab === type;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(type),\n                                            className: \"\".concat(isActive ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\", \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 23\n                                                }, this),\n                                                count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(isActive ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, type, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    records: getRecordsForType(activeTab),\n                                    recordType: activeTab,\n                                    onEdit: handleEditRecord,\n                                    onDelete: handleDeleteRecord,\n                                    onAdd: handleAddRecord,\n                                    domain: domain,\n                                    loading: loading\n                                }, activeTab, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"L+6j1R40H8OZfVr1iw9tlzSYO8c=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/constants/dnsRecords.js":
/*!*************************************!*\
  !*** ./src/constants/dnsRecords.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_TTL: function() { return /* binding */ DEFAULT_TTL; },\n/* harmony export */   DNS_PRESETS: function() { return /* binding */ DNS_PRESETS; },\n/* harmony export */   DNS_RECORD_TYPES: function() { return /* binding */ DNS_RECORD_TYPES; },\n/* harmony export */   TTL_OPTIONS: function() { return /* binding */ TTL_OPTIONS; },\n/* harmony export */   checkRecordRestrictions: function() { return /* binding */ checkRecordRestrictions; },\n/* harmony export */   getDnsRecordTypes: function() { return /* binding */ getDnsRecordTypes; },\n/* harmony export */   validateDnsRecord: function() { return /* binding */ validateDnsRecord; }\n/* harmony export */ });\n// DNS Record Types and Configuration\nconst DNS_RECORD_TYPES = {\n    A: {\n        name: \"A\",\n        label: \"A Record\",\n        description: \"Points a domain to an IPv4 address\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, www, mail, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"IPv4 Address\",\n                type: \"text\",\n                required: true,\n                placeholder: \"***********\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"number\",\n                required: true,\n                default: 14400,\n                placeholder: \"14400\",\n                min: 60,\n                max: 2147483647\n            }\n        ],\n        validation: {\n            content: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/\n        }\n    },\n    AAAA: {\n        name: \"AAAA\",\n        label: \"AAAA Record\",\n        description: \"Points a domain to an IPv6 address\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, www, mail, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"IPv6 Address\",\n                type: \"text\",\n                required: true,\n                placeholder: \"2001:0db8:85a3:0000:0000:8a2e:0370:7334\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"number\",\n                required: true,\n                default: 14400,\n                placeholder: \"14400\",\n                min: 60,\n                max: 2147483647\n            }\n        ],\n        validation: {\n            content: /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/\n        }\n    },\n    CNAME: {\n        name: \"CNAME\",\n        label: \"CNAME Record\",\n        description: \"Points a domain to another domain name\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"www, blog, shop, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"Target Domain\",\n                type: \"text\",\n                required: true,\n                placeholder: \"example.com\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/\n        },\n        restrictions: {\n            nameCannotBe: [\n                \"@\"\n            ],\n            message: \"CNAME records cannot be used for the root domain (@). Use A or AAAA records instead.\"\n        }\n    },\n    MX: {\n        name: \"MX\",\n        label: \"MX Record\",\n        description: \"Specifies mail servers for the domain\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, mail, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"Mail Server\",\n                type: \"text\",\n                required: true,\n                placeholder: \"mail.example.com\"\n            },\n            {\n                name: \"priority\",\n                label: \"Priority\",\n                type: \"number\",\n                required: true,\n                placeholder: \"10\",\n                min: 0,\n                max: 65535\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/,\n            priority: /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/\n        }\n    },\n    TXT: {\n        name: \"TXT\",\n        label: \"TXT Record\",\n        description: \"Stores text information for various purposes (SPF, DKIM, verification, etc.)\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, _dmarc, _spf, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"Text Content\",\n                type: \"textarea\",\n                required: true,\n                placeholder: \"v=spf1 include:_spf.google.com ~all\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^.{1,255}$/\n        }\n    },\n    NS: {\n        name: \"NS\",\n        label: \"NS Record\",\n        description: \"Delegates a subdomain to different nameservers\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Subdomain\",\n                type: \"text\",\n                required: true,\n                placeholder: \"subdomain\"\n            },\n            {\n                name: \"content\",\n                label: \"Nameserver\",\n                type: \"text\",\n                required: true,\n                placeholder: \"ns1.example.com\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/\n        },\n        restrictions: {\n            nameCannotBe: [\n                \"@\"\n            ],\n            message: \"NS records for the root domain (@) should be managed through nameserver settings, not DNS records.\"\n        }\n    },\n    SRV: {\n        name: \"SRV\",\n        label: \"SRV Record\",\n        description: \"Specifies the location of services (port and hostname)\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Service Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"_service._protocol (e.g., _sip._tcp)\"\n            },\n            {\n                name: \"content\",\n                label: \"Target Host\",\n                type: \"text\",\n                required: true,\n                placeholder: \"target.example.com\"\n            },\n            {\n                name: \"priority\",\n                label: \"Priority\",\n                type: \"number\",\n                required: true,\n                placeholder: \"10\",\n                min: 0,\n                max: 65535\n            },\n            {\n                name: \"weight\",\n                label: \"Weight\",\n                type: \"number\",\n                required: true,\n                placeholder: \"5\",\n                min: 0,\n                max: 65535\n            },\n            {\n                name: \"port\",\n                label: \"Port\",\n                type: \"number\",\n                required: true,\n                placeholder: \"5060\",\n                min: 1,\n                max: 65535\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            name: /^_[a-zA-Z0-9-]+\\._[a-zA-Z0-9-]+$/,\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/,\n            priority: /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/,\n            weight: /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/,\n            port: /^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/\n        }\n    }\n};\n// TTL (Time To Live) options in seconds\nconst TTL_OPTIONS = [\n    {\n        value: 300,\n        label: \"5 minutes\"\n    },\n    {\n        value: 600,\n        label: \"10 minutes\"\n    },\n    {\n        value: 1800,\n        label: \"30 minutes\"\n    },\n    {\n        value: 3600,\n        label: \"1 hour\"\n    },\n    {\n        value: 7200,\n        label: \"2 hours\"\n    },\n    {\n        value: 14400,\n        label: \"4 hours\"\n    },\n    {\n        value: 28800,\n        label: \"8 hours\"\n    },\n    {\n        value: 43200,\n        label: \"12 hours\"\n    },\n    {\n        value: 86400,\n        label: \"1 day\"\n    },\n    {\n        value: 172800,\n        label: \"2 days\"\n    },\n    {\n        value: 604800,\n        label: \"1 week\"\n    }\n];\n// Default TTL value\nconst DEFAULT_TTL = 14400;\n// Common DNS record presets for quick setup\nconst DNS_PRESETS = {\n    \"Google Workspace\": {\n        description: \"Configure Google Workspace email and services\",\n        records: [\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"aspmx.l.google.com\",\n                priority: 1,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt1.aspmx.l.google.com\",\n                priority: 5,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt2.aspmx.l.google.com\",\n                priority: 5,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt3.aspmx.l.google.com\",\n                priority: 10,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt4.aspmx.l.google.com\",\n                priority: 10,\n                ttl: 14400\n            },\n            {\n                type: \"TXT\",\n                name: \"@\",\n                content: \"v=spf1 include:_spf.google.com ~all\",\n                ttl: 14400\n            }\n        ]\n    },\n    \"Microsoft 365\": {\n        description: \"Configure Microsoft 365 email and services\",\n        records: [\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"{domain}.mail.protection.outlook.com\",\n                priority: 0,\n                ttl: 14400\n            },\n            {\n                type: \"TXT\",\n                name: \"@\",\n                content: \"v=spf1 include:spf.protection.outlook.com -all\",\n                ttl: 14400\n            },\n            {\n                type: \"CNAME\",\n                name: \"autodiscover\",\n                content: \"autodiscover.outlook.com\",\n                ttl: 14400\n            }\n        ]\n    },\n    Cloudflare: {\n        description: \"Basic Cloudflare setup\",\n        records: [\n            {\n                type: \"A\",\n                name: \"@\",\n                content: \"*********\",\n                ttl: 300\n            },\n            {\n                type: \"A\",\n                name: \"www\",\n                content: \"*********\",\n                ttl: 300\n            }\n        ]\n    }\n};\n// Validation helper functions\nconst validateDnsRecord = (type, field, value)=>{\n    const recordType = DNS_RECORD_TYPES[type];\n    if (!recordType || !recordType.validation || !recordType.validation[field]) {\n        return {\n            isValid: true\n        };\n    }\n    const regex = recordType.validation[field];\n    const isValid = regex.test(value);\n    return {\n        isValid,\n        message: isValid ? null : getValidationMessage(type, field)\n    };\n};\nconst getValidationMessage = (type, field)=>{\n    var _messages_type;\n    const messages = {\n        A: {\n            content: \"Please enter a valid IPv4 address (e.g., ***********)\"\n        },\n        AAAA: {\n            content: \"Please enter a valid IPv6 address (e.g., 2001:0db8:85a3::8a2e:0370:7334)\"\n        },\n        CNAME: {\n            content: \"Please enter a valid domain name (e.g., example.com)\"\n        },\n        MX: {\n            content: \"Please enter a valid mail server domain (e.g., mail.example.com)\",\n            priority: \"Priority must be a number between 0 and 65535\"\n        },\n        TXT: {\n            content: \"Text content cannot be empty and must be less than 255 characters\"\n        },\n        NS: {\n            content: \"Please enter a valid nameserver domain (e.g., ns1.example.com)\"\n        },\n        SRV: {\n            name: \"Service name must be in format _service._protocol (e.g., _sip._tcp)\",\n            content: \"Please enter a valid target hostname (e.g., target.example.com)\",\n            priority: \"Priority must be a number between 0 and 65535\",\n            weight: \"Weight must be a number between 0 and 65535\",\n            port: \"Port must be a number between 1 and 65535\"\n        }\n    };\n    return ((_messages_type = messages[type]) === null || _messages_type === void 0 ? void 0 : _messages_type[field]) || \"Invalid value\";\n};\n// Check if a record type has restrictions for certain names\nconst checkRecordRestrictions = (type, name)=>{\n    const recordType = DNS_RECORD_TYPES[type];\n    if (!(recordType === null || recordType === void 0 ? void 0 : recordType.restrictions)) {\n        return {\n            isValid: true\n        };\n    }\n    const { nameCannotBe, message } = recordType.restrictions;\n    const isValid = !nameCannotBe.includes(name);\n    return {\n        isValid,\n        message: isValid ? null : message\n    };\n};\n// Get all available DNS record types as array\nconst getDnsRecordTypes = ()=>{\n    return Object.keys(DNS_RECORD_TYPES).map((key)=>({\n            value: key,\n            label: DNS_RECORD_TYPES[key].label,\n            description: DNS_RECORD_TYPES[key].description\n        }));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/constants/dnsRecords.js\n"));

/***/ })

});
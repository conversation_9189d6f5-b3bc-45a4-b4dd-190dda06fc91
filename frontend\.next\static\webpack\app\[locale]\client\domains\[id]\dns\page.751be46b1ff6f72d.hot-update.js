"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,FileText,Globe,Link,Mail,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV\",\n            icon: _barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Don't automatically set DNS service as active just because records loaded\n                // The activation status should come from the domain data, not from successful record loading\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // Don't change DNS service activation status based on record loading failure\n            // The activation status should only be determined by domain.dnsActivated\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load DNS records. Please check if DNS service is activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            console.log(\"\\uD83D\\uDD0D Domain object:\", domain);\n            // The backend expects orderId, which should be the domain's order ID\n            const orderId = domain.orderid || domain.domainOrderId || domain.id;\n            console.log(\"\\uD83D\\uDCCB Using order ID: \".concat(orderId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].activateDnsService(orderId);\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"DNS service activated successfully!\");\n                // Update the domain object with DNS activation status\n                if (onUpdate) {\n                    onUpdate({\n                        dnsActivated: true,\n                        dnsActivatedAt: new Date().toISOString(),\n                        dnsZoneId: response.data.zoneId || null\n                    });\n                }\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].updateDnsRecord(domain.id, recordData.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error updating DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to update DNS record\");\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async function(recordId) {\n        let record = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId), record);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].deleteDnsRecord(domain.id, recordId, record);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type (filter out empty records)\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type && record.content && record.content.trim() !== \"\");\n    };\n    // Update DNS service status when domain changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDnsServiceActive((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.dnsActivated\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-center gap-3\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5 text-amber-600 flex-shrink-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 237,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 236,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-amber-50 border border-amber-200 rounded-lg p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-amber-900 mb-1\",\n                                            children: \"DNS Service Not Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-amber-800 text-sm leading-relaxed\",\n                                            children: \"You need to activate DNS service before you can manage DNS records for this domain. Once activated, you'll be able to add, edit, and delete DNS records.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 251,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: activateDnsService,\n                            disabled: activatingService,\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0\",\n                            children: [\n                                activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this),\n                                \"Activate DNS\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 263,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 250,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-6\",\n                children: [\n                    !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 h-20 bg-amber-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_FileText_Globe_Link_Mail_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-10 w-10 text-amber-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-medium text-gray-900 mb-3\",\n                                                children: \"DNS Management Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"DNS service is not activated for this domain. Please activate DNS service using the button above to start managing your DNS records.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 284,\n                        columnNumber: 11\n                    }, this),\n                    dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8 px-6\",\n                                    \"aria-label\": \"Tabs\",\n                                    children: recordTypes.map((param)=>{\n                                        let { type, label } = param;\n                                        const count = getRecordsForType(type).length;\n                                        const isActive = activeTab === type;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(type),\n                                            className: \"\".concat(isActive ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\", \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 23\n                                                }, this),\n                                                count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(isActive ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, type, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    records: getRecordsForType(activeTab),\n                                    recordType: activeTab,\n                                    onEdit: handleEditRecord,\n                                    onDelete: handleDeleteRecord,\n                                    onAdd: handleAddRecord,\n                                    domain: domain,\n                                    loading: loading\n                                }, activeTab, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 244,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"L+6j1R40H8OZfVr1iw9tlzSYO8c=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx":
/*!***************************************************!*\
  !*** ./src/components/domains/DnsRecordTable.jsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DnsRecordTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/dnsRecords */ \"(app-pages-browser)/./src/constants/dnsRecords.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DnsRecordTable(param) {\n    let { records, recordType, onEdit, onDelete, onAdd, domain, loading = false } = param;\n    _s();\n    const [deleteConfirm, setDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInlineForm, setShowInlineForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Reset form state when record type changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowInlineForm(false);\n        setFormData({});\n        setFormErrors({});\n    }, [\n        recordType\n    ]);\n    // Initialize form data for the current record type\n    const initializeFormData = (type)=>{\n        const recordTypeConfig = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DNS_RECORD_TYPES[type];\n        if (!recordTypeConfig) return {\n            type\n        };\n        const initialData = {\n            type\n        };\n        recordTypeConfig.fields.forEach((field)=>{\n            if (field.name === \"ttl\") {\n                initialData[field.name] = field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL;\n            } else if (field.type === \"number\") {\n                initialData[field.name] = field.default || 0;\n            } else {\n                initialData[field.name] = field.default || \"\";\n            }\n        });\n        return initialData;\n    };\n    // Handle add button click - shows inline form\n    const handleAddClick = ()=>{\n        const initialData = initializeFormData(recordType);\n        setFormData(initialData);\n        setFormErrors({});\n        setShowInlineForm(true);\n    };\n    // Handle form field changes\n    const handleFieldChange = (fieldName, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [fieldName]: value\n            }));\n        // Clear error for this field\n        if (formErrors[fieldName]) {\n            setFormErrors((prev)=>({\n                    ...prev,\n                    [fieldName]: null\n                }));\n        }\n    };\n    // Handle form submission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            // Validate the form data\n            const validation = (0,_constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.validateDnsRecord)(formData);\n            if (!validation.isValid) {\n                setFormErrors(validation.errors);\n                return;\n            }\n            await onAdd(formData);\n            setShowInlineForm(false);\n            setFormData({});\n            setFormErrors({});\n        } catch (error) {\n            console.error(\"Error adding record:\", error);\n        // Handle validation errors if needed\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Handle form cancel\n    const handleCancel = ()=>{\n        setShowInlineForm(false);\n        setFormData({});\n        setFormErrors({});\n    };\n    // Get record type configuration\n    const getRecordTypeConfig = (type)=>{\n        const configs = {\n            A: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                label: \"IPv4 Address\",\n                color: \"blue\",\n                bgColor: \"bg-blue-50\",\n                textColor: \"text-blue-600\",\n                borderColor: \"border-blue-200\"\n            },\n            AAAA: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                label: \"IPv6 Address\",\n                color: \"purple\",\n                bgColor: \"bg-purple-50\",\n                textColor: \"text-purple-600\",\n                borderColor: \"border-purple-200\"\n            },\n            CNAME: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                label: \"Canonical Name\",\n                color: \"green\",\n                bgColor: \"bg-green-50\",\n                textColor: \"text-green-600\",\n                borderColor: \"border-green-200\"\n            },\n            MX: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                label: \"Mail Exchange\",\n                color: \"orange\",\n                bgColor: \"bg-orange-50\",\n                textColor: \"text-orange-600\",\n                borderColor: \"border-orange-200\"\n            },\n            TXT: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                label: \"Text Record\",\n                color: \"gray\",\n                bgColor: \"bg-gray-50\",\n                textColor: \"text-gray-600\",\n                borderColor: \"border-gray-200\"\n            },\n            NS: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                label: \"Name Server\",\n                color: \"indigo\",\n                bgColor: \"bg-indigo-50\",\n                textColor: \"text-indigo-600\",\n                borderColor: \"border-indigo-200\"\n            },\n            SRV: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                label: \"Service Record\",\n                color: \"pink\",\n                bgColor: \"bg-pink-50\",\n                textColor: \"text-pink-600\",\n                borderColor: \"border-pink-200\"\n            }\n        };\n        return configs[type] || configs.A;\n    };\n    // Format TTL display\n    const formatTTL = (ttl)=>{\n        const numericTTL = parseInt(ttl);\n        const option = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.TTL_OPTIONS.find((opt)=>opt.value === numericTTL);\n        if (option) {\n            return option.label;\n        }\n        // Format custom TTL values\n        if (numericTTL >= 86400) {\n            const days = Math.floor(numericTTL / 86400);\n            const remainder = numericTTL % 86400;\n            if (remainder === 0) {\n                return \"\".concat(days, \" day\").concat(days !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 3600) {\n            const hours = Math.floor(numericTTL / 3600);\n            const remainder = numericTTL % 3600;\n            if (remainder === 0) {\n                return \"\".concat(hours, \" hour\").concat(hours !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 60) {\n            const minutes = Math.floor(numericTTL / 60);\n            const remainder = numericTTL % 60;\n            if (remainder === 0) {\n                return \"\".concat(minutes, \" minute\").concat(minutes !== 1 ? \"s\" : \"\");\n            }\n        }\n        return \"\".concat(numericTTL, \"s\");\n    };\n    // Format record name\n    const formatRecordName = (name)=>{\n        if (!name || name === \"@\") return (domain === null || domain === void 0 ? void 0 : domain.name) || \"@\";\n        if (name.endsWith(\".\")) return name.slice(0, -1);\n        return name;\n    };\n    // Copy to clipboard\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n        } catch (err) {\n            console.error(\"Failed to copy:\", err);\n        }\n    };\n    // Handle delete confirmation\n    const handleDeleteClick = (record)=>{\n        setDeleteConfirm(record);\n    };\n    const handleDeleteConfirm = ()=>{\n        if (deleteConfirm && onDelete) {\n            onDelete(deleteConfirm.id, deleteConfirm);\n        }\n        setDeleteConfirm(null);\n    };\n    const config = getRecordTypeConfig(recordType);\n    const IconComponent = config.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 rounded-lg \".concat(config.bgColor, \" border border-gray-200\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"h-5 w-5 \".concat(config.textColor)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: [\n                                                    recordType,\n                                                    \" Records\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    config.label,\n                                                    \" • \",\n                                                    records.length,\n                                                    \" record\",\n                                                    records.length !== 1 ? \"s\" : \"\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this),\n                            !showInlineForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAddClick,\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add \",\n                                    recordType\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    showInlineForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: [\n                                                \"Add \",\n                                                recordType,\n                                                \" Record\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: recordType === \"SRV\" ? \"Configure service location with priority, weight, port, and target host\" : \"Add a new \".concat(recordType, \" record for \").concat(domain === null || domain === void 0 ? void 0 : domain.name)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this),\n                                (()=>{\n                                    const recordTypeConfig = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DNS_RECORD_TYPES[recordType];\n                                    if (!recordTypeConfig) return null;\n                                    const fields = recordTypeConfig.fields;\n                                    const gridCols = fields.length <= 3 ? \"md:grid-cols-\".concat(fields.length) : \"md:grid-cols-3\";\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 \".concat(gridCols, \" gap-4\"),\n                                        children: fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: [\n                                                            field.label,\n                                                            field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 ml-1\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 46\n                                                            }, this),\n                                                            recordType === \"SRV\" && [\n                                                                \"priority\",\n                                                                \"weight\",\n                                                                \"port\"\n                                                            ].includes(field.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 ml-2\",\n                                                                children: [\n                                                                    \"[\",\n                                                                    field.min || 0,\n                                                                    \"-\",\n                                                                    field.max || 65535,\n                                                                    \"]\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    recordType === \"SRV\" && field.name === \"name\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2 text-xs text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mb-1\",\n                                                                children: \"Examples:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"• _chat._tcp.\",\n                                                                    domain === null || domain === void 0 ? void 0 : domain.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    \"• _sip._udp.subdomain.\",\n                                                                    domain === null || domain === void 0 ? void 0 : domain.name\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    recordType === \"SRV\" && field.name === \"content\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-2 text-xs text-gray-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Type in a fully qualified domain name (e.g., abc.pqr.com)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    field.type === \"select\" && field.name === \"ttl\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData[field.name] || field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL,\n                                                                onChange: (e)=>handleFieldChange(field.name, parseInt(e.target.value)),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                children: _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.TTL_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: option.value,\n                                                                        children: option.label\n                                                                    }, option.value, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 33\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            recordType === \"SRV\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"Default: \",\n                                                                    formatTTL(formData[field.name] || field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 27\n                                                    }, this) : field.type === \"number\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData[field.name] || (recordType === \"SRV\" && [\n                                                            \"priority\",\n                                                            \"weight\",\n                                                            \"port\"\n                                                        ].includes(field.name) ? 0 : \"\"),\n                                                        onChange: (e)=>handleFieldChange(field.name, parseInt(e.target.value) || 0),\n                                                        placeholder: recordType === \"SRV\" && field.name === \"port\" ? \"80, 443, 5060, etc.\" : field.placeholder,\n                                                        min: field.min,\n                                                        max: field.max,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 27\n                                                    }, this) : field.type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                                                        placeholder: field.placeholder,\n                                                        rows: 2,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                                                        placeholder: recordType === \"SRV\" && field.name === \"name\" ? \"_service._protocol (e.g., _sip._tcp)\" : recordType === \"SRV\" && field.name === \"content\" ? \"target.\".concat((domain === null || domain === void 0 ? void 0 : domain.name) || \"example.com\") : field.placeholder,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    formErrors[field.name] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: formErrors[field.name]\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 19\n                                    }, this);\n                                })(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between pt-4 border-t border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: recordType === \"SRV\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Service name format: _service._protocol.\",\n                                                    domain === null || domain === void 0 ? void 0 : domain.name,\n                                                    \" • Target: fully qualified domain name\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    'Use \"@\" for root domain (',\n                                                    domain === null || domain === void 0 ? void 0 : domain.name,\n                                                    '), \"www\" for www.',\n                                                    domain === null || domain === void 0 ? void 0 : domain.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleCancel,\n                                                    disabled: isSubmitting,\n                                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 inline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Cancel\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isSubmitting,\n                                                    className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"animate-spin h-4 w-4 mr-2 inline\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        className: \"opacity-25\",\n                                                                        cx: \"12\",\n                                                                        cy: \"12\",\n                                                                        r: \"10\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        className: \"opacity-75\",\n                                                                        fill: \"currentColor\",\n                                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Adding...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2 inline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Add Record\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this),\n                                \"Loading \",\n                                recordType,\n                                \" records...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 446,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 445,\n                        columnNumber: 11\n                    }, this) : records.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 rounded-full \".concat(config.bgColor),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-8 w-8 \".concat(config.textColor)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: [\n                                                \"No \",\n                                                recordType,\n                                                \" records found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: [\n                                                \"Create your first \",\n                                                recordType,\n                                                \" record to get started\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddClick,\n                                            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add \",\n                                                recordType,\n                                                \" Record\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 456,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 455,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"TTL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 19\n                                            }, this),\n                                            (recordType === \"MX\" || recordType === \"SRV\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Priority\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"relative px-6 py-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 497,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: records.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50 transition-colors duration-150\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: formatRecordName(record.name)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-mono text-gray-700 flex-1 min-w-0 truncate\",\n                                                                children: record.content\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>copyToClipboard(record.content),\n                                                                className: \"p-1 text-gray-400 hover:text-gray-600 rounded\",\n                                                                title: \"Copy to clipboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            formatTTL(record.ttl)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 21\n                                                }, this),\n                                                (recordType === \"MX\" || recordType === \"SRV\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-900\",\n                                                        children: record.priority || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>onEdit && onEdit(record),\n                                                                className: \"p-2 text-gray-400 hover:text-blue-600 rounded-md hover:bg-blue-50\",\n                                                                title: \"Edit record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDeleteClick(record),\n                                                                className: \"p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-red-50\",\n                                                                title: \"Delete record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 559,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, record.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 479,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 478,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            deleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center sm:text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg leading-6 font-medium text-gray-900\",\n                                            children: \"Delete DNS Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 581,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mb-4\",\n                                        children: [\n                                            \"Are you sure you want to delete this \",\n                                            deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.type,\n                                            \" record? This action cannot be undone.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 592,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-gray-50 rounded-lg border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                    children: formatRecordName(deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.name)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-mono text-gray-600\",\n                                                    children: deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.content\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 591,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDeleteConfirm(null),\n                                        className: \"flex-1 px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDeleteConfirm,\n                                        className: \"flex-1 px-4 py-2 bg-red-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                        children: \"Delete Record\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 606,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 580,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                    lineNumber: 579,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 578,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DnsRecordTable, \"EMPfo3qgCdukLUyhx/J9sXiisRk=\");\n_c = DnsRecordTable;\nvar _c;\n$RefreshReg$(_c, \"DnsRecordTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2RvbWFpbnMvRG5zUmVjb3JkVGFibGUuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM0QztBQWlCdEI7QUFNVTtBQUVqQixTQUFTcUIsZUFBZSxLQVF0QztRQVJzQyxFQUNyQ0MsT0FBTyxFQUNQQyxVQUFVLEVBQ1ZDLE1BQU0sRUFDTkMsUUFBUSxFQUNSQyxLQUFLLEVBQ0xDLE1BQU0sRUFDTkMsVUFBVSxLQUFLLEVBQ2hCLEdBUnNDOztJQVNyQyxNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHOUIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDK0IsZ0JBQWdCQyxrQkFBa0IsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ2lDLFVBQVVDLFlBQVksR0FBR2xDLCtDQUFRQSxDQUFDLENBQUM7SUFDMUMsTUFBTSxDQUFDbUMsWUFBWUMsY0FBYyxHQUFHcEMsK0NBQVFBLENBQUMsQ0FBQztJQUM5QyxNQUFNLENBQUNxQyxjQUFjQyxnQkFBZ0IsR0FBR3RDLCtDQUFRQSxDQUFDO0lBRWpELDRDQUE0QztJQUM1Q0MsZ0RBQVNBLENBQUM7UUFDUitCLGtCQUFrQjtRQUNsQkUsWUFBWSxDQUFDO1FBQ2JFLGNBQWMsQ0FBQztJQUNqQixHQUFHO1FBQUNiO0tBQVc7SUFFZixtREFBbUQ7SUFDbkQsTUFBTWdCLHFCQUFxQixDQUFDQztRQUMxQixNQUFNQyxtQkFBbUJ2QixtRUFBZ0IsQ0FBQ3NCLEtBQUs7UUFDL0MsSUFBSSxDQUFDQyxrQkFBa0IsT0FBTztZQUFFRDtRQUFLO1FBRXJDLE1BQU1FLGNBQWM7WUFBRUY7UUFBSztRQUMzQkMsaUJBQWlCRSxNQUFNLENBQUNDLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDOUIsSUFBSUEsTUFBTUMsSUFBSSxLQUFLLE9BQU87Z0JBQ3hCSixXQUFXLENBQUNHLE1BQU1DLElBQUksQ0FBQyxHQUFHRCxNQUFNRSxPQUFPLElBQUk1Qiw4REFBV0E7WUFDeEQsT0FBTyxJQUFJMEIsTUFBTUwsSUFBSSxLQUFLLFVBQVU7Z0JBQ2xDRSxXQUFXLENBQUNHLE1BQU1DLElBQUksQ0FBQyxHQUFHRCxNQUFNRSxPQUFPLElBQUk7WUFDN0MsT0FBTztnQkFDTEwsV0FBVyxDQUFDRyxNQUFNQyxJQUFJLENBQUMsR0FBR0QsTUFBTUUsT0FBTyxJQUFJO1lBQzdDO1FBQ0Y7UUFFQSxPQUFPTDtJQUNUO0lBRUEsOENBQThDO0lBQzlDLE1BQU1NLGlCQUFpQjtRQUNyQixNQUFNTixjQUFjSCxtQkFBbUJoQjtRQUN2Q1csWUFBWVE7UUFDWk4sY0FBYyxDQUFDO1FBQ2ZKLGtCQUFrQjtJQUNwQjtJQUVBLDRCQUE0QjtJQUM1QixNQUFNaUIsb0JBQW9CLENBQUNDLFdBQVdDO1FBQ3BDakIsWUFBWWtCLENBQUFBLE9BQVM7Z0JBQ25CLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0YsVUFBVSxFQUFFQztZQUNmO1FBRUEsNkJBQTZCO1FBQzdCLElBQUloQixVQUFVLENBQUNlLFVBQVUsRUFBRTtZQUN6QmQsY0FBY2dCLENBQUFBLE9BQVM7b0JBQ3JCLEdBQUdBLElBQUk7b0JBQ1AsQ0FBQ0YsVUFBVSxFQUFFO2dCQUNmO1FBQ0Y7SUFDRjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNRyxlQUFlLE9BQU9DO1FBQzFCQSxFQUFFQyxjQUFjO1FBQ2hCakIsZ0JBQWdCO1FBRWhCLElBQUk7WUFDRix5QkFBeUI7WUFDekIsTUFBTWtCLGFBQWFwQyx3RUFBaUJBLENBQUNhO1lBQ3JDLElBQUksQ0FBQ3VCLFdBQVdDLE9BQU8sRUFBRTtnQkFDdkJyQixjQUFjb0IsV0FBV0UsTUFBTTtnQkFDL0I7WUFDRjtZQUVBLE1BQU1oQyxNQUFNTztZQUNaRCxrQkFBa0I7WUFDbEJFLFlBQVksQ0FBQztZQUNiRSxjQUFjLENBQUM7UUFDakIsRUFBRSxPQUFPdUIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsd0JBQXdCQTtRQUN0QyxxQ0FBcUM7UUFDdkMsU0FBVTtZQUNSckIsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxxQkFBcUI7SUFDckIsTUFBTXVCLGVBQWU7UUFDbkI3QixrQkFBa0I7UUFDbEJFLFlBQVksQ0FBQztRQUNiRSxjQUFjLENBQUM7SUFDakI7SUFJQSxnQ0FBZ0M7SUFDaEMsTUFBTTBCLHNCQUFzQixDQUFDdEI7UUFDM0IsTUFBTXVCLFVBQVU7WUFDZEMsR0FBRztnQkFDREMsTUFBTS9ELDBLQUFLQTtnQkFDWGdFLE9BQU87Z0JBQ1BDLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLGFBQWE7WUFDZjtZQUNBQyxNQUFNO2dCQUNKTixNQUFNL0QsMEtBQUtBO2dCQUNYZ0UsT0FBTztnQkFDUEMsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsV0FBVztnQkFDWEMsYUFBYTtZQUNmO1lBQ0FFLE9BQU87Z0JBQ0xQLE1BQU05RCwwS0FBSUE7Z0JBQ1YrRCxPQUFPO2dCQUNQQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxXQUFXO2dCQUNYQyxhQUFhO1lBQ2Y7WUFDQUcsSUFBSTtnQkFDRlIsTUFBTTdELDBLQUFJQTtnQkFDVjhELE9BQU87Z0JBQ1BDLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLGFBQWE7WUFDZjtZQUNBSSxLQUFLO2dCQUNIVCxNQUFNNUQsMEtBQVFBO2dCQUNkNkQsT0FBTztnQkFDUEMsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsV0FBVztnQkFDWEMsYUFBYTtZQUNmO1lBQ0FLLElBQUk7Z0JBQ0ZWLE1BQU0zRCwwS0FBTUE7Z0JBQ1o0RCxPQUFPO2dCQUNQQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxXQUFXO2dCQUNYQyxhQUFhO1lBQ2Y7WUFDQU0sS0FBSztnQkFDSFgsTUFBTTFELDBLQUFRQTtnQkFDZDJELE9BQU87Z0JBQ1BDLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLGFBQWE7WUFDZjtRQUNGO1FBQ0EsT0FBT1AsT0FBTyxDQUFDdkIsS0FBSyxJQUFJdUIsUUFBUUMsQ0FBQztJQUNuQztJQUVBLHFCQUFxQjtJQUNyQixNQUFNYSxZQUFZLENBQUNDO1FBQ2pCLE1BQU1DLGFBQWFDLFNBQVNGO1FBQzVCLE1BQU1HLFNBQVNoRSw4REFBV0EsQ0FBQ2lFLElBQUksQ0FBQyxDQUFDQyxNQUFRQSxJQUFJaEMsS0FBSyxLQUFLNEI7UUFDdkQsSUFBSUUsUUFBUTtZQUNWLE9BQU9BLE9BQU9mLEtBQUs7UUFDckI7UUFFQSwyQkFBMkI7UUFDM0IsSUFBSWEsY0FBYyxPQUFPO1lBQ3ZCLE1BQU1LLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ1AsYUFBYTtZQUNyQyxNQUFNUSxZQUFZUixhQUFhO1lBQy9CLElBQUlRLGNBQWMsR0FBRztnQkFDbkIsT0FBTyxHQUFjSCxPQUFYQSxNQUFLLFFBQTRCLE9BQXRCQSxTQUFTLElBQUksTUFBTTtZQUMxQztRQUNGLE9BQU8sSUFBSUwsY0FBYyxNQUFNO1lBQzdCLE1BQU1TLFFBQVFILEtBQUtDLEtBQUssQ0FBQ1AsYUFBYTtZQUN0QyxNQUFNUSxZQUFZUixhQUFhO1lBQy9CLElBQUlRLGNBQWMsR0FBRztnQkFDbkIsT0FBTyxHQUFnQkMsT0FBYkEsT0FBTSxTQUE4QixPQUF2QkEsVUFBVSxJQUFJLE1BQU07WUFDN0M7UUFDRixPQUFPLElBQUlULGNBQWMsSUFBSTtZQUMzQixNQUFNVSxVQUFVSixLQUFLQyxLQUFLLENBQUNQLGFBQWE7WUFDeEMsTUFBTVEsWUFBWVIsYUFBYTtZQUMvQixJQUFJUSxjQUFjLEdBQUc7Z0JBQ25CLE9BQU8sR0FBb0JFLE9BQWpCQSxTQUFRLFdBQWtDLE9BQXpCQSxZQUFZLElBQUksTUFBTTtZQUNuRDtRQUNGO1FBRUEsT0FBTyxHQUFjLE9BQVhWLFlBQVc7SUFDdkI7SUFFQSxxQkFBcUI7SUFDckIsTUFBTVcsbUJBQW1CLENBQUM1QztRQUN4QixJQUFJLENBQUNBLFFBQVFBLFNBQVMsS0FBSyxPQUFPbkIsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRbUIsSUFBSSxLQUFJO1FBQ2xELElBQUlBLEtBQUs2QyxRQUFRLENBQUMsTUFBTSxPQUFPN0MsS0FBSzhDLEtBQUssQ0FBQyxHQUFHLENBQUM7UUFDOUMsT0FBTzlDO0lBQ1Q7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTStDLGtCQUFrQixPQUFPQztRQUM3QixJQUFJO1lBQ0YsTUFBTUMsVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUNIO1FBQ3RDLEVBQUUsT0FBT0ksS0FBSztZQUNadEMsUUFBUUQsS0FBSyxDQUFDLG1CQUFtQnVDO1FBQ25DO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTUMsb0JBQW9CLENBQUNDO1FBQ3pCdEUsaUJBQWlCc0U7SUFDbkI7SUFFQSxNQUFNQyxzQkFBc0I7UUFDMUIsSUFBSXhFLGlCQUFpQkosVUFBVTtZQUM3QkEsU0FBU0ksY0FBY3lFLEVBQUUsRUFBRXpFO1FBQzdCO1FBQ0FDLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU15RSxTQUFTekMsb0JBQW9CdkM7SUFDbkMsTUFBTWlGLGdCQUFnQkQsT0FBT3RDLElBQUk7SUFFakMscUJBQ0U7OzBCQUNFLDhEQUFDd0M7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVcsa0JBQWlDLE9BQWZILE9BQU9uQyxPQUFPLEVBQUM7a0RBQy9DLDRFQUFDb0M7NENBQWNFLFdBQVcsV0FBNEIsT0FBakJILE9BQU9sQyxTQUFTOzs7Ozs7Ozs7OztrREFFdkQsOERBQUNvQzs7MERBQ0MsOERBQUNFO2dEQUFHRCxXQUFVOztvREFDWG5GO29EQUFXOzs7Ozs7OzBEQUVkLDhEQUFDcUY7Z0RBQUVGLFdBQVU7O29EQUNWSCxPQUFPckMsS0FBSztvREFBQztvREFBSTVDLFFBQVF1RixNQUFNO29EQUFDO29EQUFRdkYsUUFBUXVGLE1BQU0sS0FBSyxJQUFJLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBSTNFLENBQUM5RSxnQ0FDQSw4REFBQytFO2dDQUNDQyxTQUFTL0Q7Z0NBQ1QwRCxXQUFVOztrREFFViw4REFBQy9GLDBLQUFJQTt3Q0FBQytGLFdBQVU7Ozs7OztvQ0FBaUI7b0NBQzVCbkY7Ozs7Ozs7Ozs7Ozs7b0JBTVZRLGdDQUNDLDhEQUFDMEU7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNNOzRCQUFLQyxVQUFVNUQ7NEJBQWNxRCxXQUFVOzs4Q0FDdEMsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1E7NENBQUdSLFdBQVU7O2dEQUF5QztnREFDaERuRjtnREFBVzs7Ozs7OztzREFFbEIsOERBQUNxRjs0Q0FBRUYsV0FBVTtzREFDVm5GLGVBQWUsUUFDWiw0RUFDQSxvQkFBYUEsWUFBVyxnQkFBMkIsT0FBYkksbUJBQUFBLDZCQUFBQSxPQUFRbUIsSUFBSTs7Ozs7Ozs7Ozs7O2dDQU14RDtvQ0FDQSxNQUFNTCxtQkFBbUJ2QixtRUFBZ0IsQ0FBQ0ssV0FBVztvQ0FDckQsSUFBSSxDQUFDa0Isa0JBQWtCLE9BQU87b0NBRTlCLE1BQU1FLFNBQVNGLGlCQUFpQkUsTUFBTTtvQ0FDdEMsTUFBTXdFLFdBQVd4RSxPQUFPa0UsTUFBTSxJQUFJLElBQUksZ0JBQThCLE9BQWRsRSxPQUFPa0UsTUFBTSxJQUFLO29DQUV4RSxxQkFDRSw4REFBQ0o7d0NBQUlDLFdBQVcsb0JBQTZCLE9BQVRTLFVBQVM7a0RBQzFDeEUsT0FBT3lFLEdBQUcsQ0FBQyxDQUFDdkUsc0JBQ1gsOERBQUM0RDs7a0VBQ0MsOERBQUN2Qzt3REFBTXdDLFdBQVU7OzREQUNkN0QsTUFBTXFCLEtBQUs7NERBQ1hyQixNQUFNd0UsUUFBUSxrQkFBSSw4REFBQ0M7Z0VBQUtaLFdBQVU7MEVBQW9COzs7Ozs7NERBRXREbkYsZUFBZSxTQUFTO2dFQUFDO2dFQUFZO2dFQUFVOzZEQUFPLENBQUNnRyxRQUFRLENBQUMxRSxNQUFNQyxJQUFJLG1CQUN6RSw4REFBQ3dFO2dFQUFLWixXQUFVOztvRUFBNkI7b0VBQ3pDN0QsTUFBTTJFLEdBQUcsSUFBSTtvRUFBRTtvRUFBRTNFLE1BQU00RSxHQUFHLElBQUk7b0VBQU07Ozs7Ozs7Ozs7Ozs7b0RBTTNDbEcsZUFBZSxTQUFTc0IsTUFBTUMsSUFBSSxLQUFLLHdCQUN0Qyw4REFBQzJEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0U7Z0VBQUVGLFdBQVU7MEVBQU87Ozs7OzswRUFDcEIsOERBQUNFOztvRUFBRTtvRUFBY2pGLG1CQUFBQSw2QkFBQUEsT0FBUW1CLElBQUk7Ozs7Ozs7MEVBQzdCLDhEQUFDOEQ7O29FQUFFO29FQUF1QmpGLG1CQUFBQSw2QkFBQUEsT0FBUW1CLElBQUk7Ozs7Ozs7Ozs7Ozs7b0RBSXpDdkIsZUFBZSxTQUFTc0IsTUFBTUMsSUFBSSxLQUFLLDJCQUN0Qyw4REFBQzJEO3dEQUFJQyxXQUFVO2tFQUNiLDRFQUFDRTtzRUFBRTs7Ozs7Ozs7Ozs7b0RBR04vRCxNQUFNTCxJQUFJLEtBQUssWUFBWUssTUFBTUMsSUFBSSxLQUFLLHNCQUN6Qyw4REFBQzJEOzswRUFDQyw4REFBQ2lCO2dFQUNDdkUsT0FBT2xCLFFBQVEsQ0FBQ1ksTUFBTUMsSUFBSSxDQUFDLElBQUlELE1BQU1FLE9BQU8sSUFBSTVCLDhEQUFXQTtnRUFDM0R3RyxVQUFVLENBQUNyRSxJQUFNTCxrQkFBa0JKLE1BQU1DLElBQUksRUFBRWtDLFNBQVMxQixFQUFFc0UsTUFBTSxDQUFDekUsS0FBSztnRUFDdEV1RCxXQUFVOzBFQUVUekYsOERBQVdBLENBQUNtRyxHQUFHLENBQUMsQ0FBQ25DLHVCQUNoQiw4REFBQ0E7d0VBQTBCOUIsT0FBTzhCLE9BQU85QixLQUFLO2tGQUMzQzhCLE9BQU9mLEtBQUs7dUVBREZlLE9BQU85QixLQUFLOzs7Ozs7Ozs7OzREQUs1QjVCLGVBQWUsdUJBQ2QsOERBQUNxRjtnRUFBRUYsV0FBVTs7b0VBQTZCO29FQUM5QjdCLFVBQVU1QyxRQUFRLENBQUNZLE1BQU1DLElBQUksQ0FBQyxJQUFJRCxNQUFNRSxPQUFPLElBQUk1Qiw4REFBV0E7Ozs7Ozs7Ozs7OzsrREFJNUUwQixNQUFNTCxJQUFJLEtBQUsseUJBQ2pCLDhEQUFDcUY7d0RBQ0NyRixNQUFLO3dEQUNMVyxPQUFPbEIsUUFBUSxDQUFDWSxNQUFNQyxJQUFJLENBQUMsSUFBS3ZCLENBQUFBLGVBQWUsU0FBUzs0REFBQzs0REFBWTs0REFBVTt5REFBTyxDQUFDZ0csUUFBUSxDQUFDMUUsTUFBTUMsSUFBSSxJQUFJLElBQUksRUFBQzt3REFDbkg2RSxVQUFVLENBQUNyRSxJQUFNTCxrQkFBa0JKLE1BQU1DLElBQUksRUFBRWtDLFNBQVMxQixFQUFFc0UsTUFBTSxDQUFDekUsS0FBSyxLQUFLO3dEQUMzRTJFLGFBQWF2RyxlQUFlLFNBQVNzQixNQUFNQyxJQUFJLEtBQUssU0FBUyx3QkFBd0JELE1BQU1pRixXQUFXO3dEQUN0R04sS0FBSzNFLE1BQU0yRSxHQUFHO3dEQUNkQyxLQUFLNUUsTUFBTTRFLEdBQUc7d0RBQ2RmLFdBQVU7Ozs7OytEQUVWN0QsTUFBTUwsSUFBSSxLQUFLLDJCQUNqQiw4REFBQ3VGO3dEQUNDNUUsT0FBT2xCLFFBQVEsQ0FBQ1ksTUFBTUMsSUFBSSxDQUFDLElBQUk7d0RBQy9CNkUsVUFBVSxDQUFDckUsSUFBTUwsa0JBQWtCSixNQUFNQyxJQUFJLEVBQUVRLEVBQUVzRSxNQUFNLENBQUN6RSxLQUFLO3dEQUM3RDJFLGFBQWFqRixNQUFNaUYsV0FBVzt3REFDOUJFLE1BQU07d0RBQ050QixXQUFVOzs7Ozs2RUFHWiw4REFBQ21CO3dEQUNDckYsTUFBSzt3REFDTFcsT0FBT2xCLFFBQVEsQ0FBQ1ksTUFBTUMsSUFBSSxDQUFDLElBQUk7d0RBQy9CNkUsVUFBVSxDQUFDckUsSUFBTUwsa0JBQWtCSixNQUFNQyxJQUFJLEVBQUVRLEVBQUVzRSxNQUFNLENBQUN6RSxLQUFLO3dEQUM3RDJFLGFBQ0V2RyxlQUFlLFNBQVNzQixNQUFNQyxJQUFJLEtBQUssU0FDbEMseUNBQ0R2QixlQUFlLFNBQVNzQixNQUFNQyxJQUFJLEtBQUssWUFDdkMsVUFBd0MsT0FBOUJuQixDQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVFtQixJQUFJLEtBQUksaUJBQzFCRCxNQUFNaUYsV0FBVzt3REFFdkJwQixXQUFVOzs7Ozs7b0RBR2J2RSxVQUFVLENBQUNVLE1BQU1DLElBQUksQ0FBQyxrQkFDckIsOERBQUM4RDt3REFBRUYsV0FBVTtrRUFBNkJ2RSxVQUFVLENBQUNVLE1BQU1DLElBQUksQ0FBQzs7Ozs7OzsrQ0EvRTFERCxNQUFNQyxJQUFJOzs7Ozs7Ozs7O2dDQXFGNUI7OENBRUEsOERBQUMyRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNabkYsZUFBZSxzQkFDZCw4REFBQytGOztvREFBSztvREFDcUMzRixtQkFBQUEsNkJBQUFBLE9BQVFtQixJQUFJO29EQUFDOzs7Ozs7cUVBR3hELDhEQUFDd0U7O29EQUFLO29EQUNzQjNGLG1CQUFBQSw2QkFBQUEsT0FBUW1CLElBQUk7b0RBQUM7b0RBQWtCbkIsbUJBQUFBLDZCQUFBQSxPQUFRbUIsSUFBSTs7Ozs7Ozs7Ozs7O3NEQUkzRSw4REFBQzJEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0k7b0RBQ0N0RSxNQUFLO29EQUNMdUUsU0FBU2xEO29EQUNUb0UsVUFBVTVGO29EQUNWcUUsV0FBVTs7c0VBRVYsOERBQUMzRiwyS0FBQ0E7NERBQUMyRixXQUFVOzs7Ozs7d0RBQXdCOzs7Ozs7OzhEQUd2Qyw4REFBQ0k7b0RBQ0N0RSxNQUFLO29EQUNMeUYsVUFBVTVGO29EQUNWcUUsV0FBVTs4REFFVHJFLDZCQUNDOzswRUFDRSw4REFBQzZGO2dFQUFJeEIsV0FBVTtnRUFBbUN5QixNQUFLO2dFQUFPQyxTQUFROztrRkFDcEUsOERBQUNDO3dFQUFPM0IsV0FBVTt3RUFBYTRCLElBQUc7d0VBQUtDLElBQUc7d0VBQUtDLEdBQUU7d0VBQUtDLFFBQU87d0VBQWVDLGFBQVk7Ozs7OztrRkFDeEYsOERBQUNDO3dFQUFLakMsV0FBVTt3RUFBYXlCLE1BQUs7d0VBQWVTLEdBQUU7Ozs7Ozs7Ozs7Ozs0REFDL0M7O3FGQUlSOzswRUFDRSw4REFBQzlILDJLQUFJQTtnRUFBQzRGLFdBQVU7Ozs7Ozs0REFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBWXZEOUUsd0JBQ0MsOERBQUM2RTt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDd0I7b0NBQUl4QixXQUFVO29DQUFnRG1DLE9BQU07b0NBQTZCVixNQUFLO29DQUFPQyxTQUFROztzREFDcEgsOERBQUNDOzRDQUFPM0IsV0FBVTs0Q0FBYTRCLElBQUc7NENBQUtDLElBQUc7NENBQUtDLEdBQUU7NENBQUtDLFFBQU87NENBQWVDLGFBQVk7Ozs7OztzREFDeEYsOERBQUNDOzRDQUFLakMsV0FBVTs0Q0FBYXlCLE1BQUs7NENBQWVTLEdBQUU7Ozs7Ozs7Ozs7OztnQ0FDL0M7Z0NBQ0dySDtnQ0FBVzs7Ozs7Ozs7Ozs7K0JBR3RCRCxRQUFRdUYsTUFBTSxLQUFLLGtCQUNyQiw4REFBQ0o7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVcsb0JBQW1DLE9BQWZILE9BQU9uQyxPQUFPOzhDQUNoRCw0RUFBQ29DO3dDQUFjRSxXQUFXLFdBQTRCLE9BQWpCSCxPQUFPbEMsU0FBUzs7Ozs7Ozs7Ozs7OENBRXZELDhEQUFDb0M7O3NEQUNDLDhEQUFDRTs0Q0FBR0QsV0FBVTs7Z0RBQXlDO2dEQUNqRG5GO2dEQUFXOzs7Ozs7O3NEQUVqQiw4REFBQ3FGOzRDQUFFRixXQUFVOztnREFBNkI7Z0RBQ3JCbkY7Z0RBQVc7Ozs7Ozs7c0RBRWhDLDhEQUFDdUY7NENBQ0NDLFNBQVMvRDs0Q0FDVDBELFdBQVU7OzhEQUVWLDhEQUFDL0YsMEtBQUlBO29EQUFDK0YsV0FBVTs7Ozs7O2dEQUFpQjtnREFDNUJuRjtnREFBVzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NkNBTXhCLDhEQUFDa0Y7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNvQzs0QkFBTXBDLFdBQVU7OzhDQUNmLDhEQUFDcUM7b0NBQU1yQyxXQUFVOzhDQUNmLDRFQUFDc0M7OzBEQUNDLDhEQUFDQztnREFBR0MsT0FBTTtnREFBTXhDLFdBQVU7MERBQWlGOzs7Ozs7MERBRzNHLDhEQUFDdUM7Z0RBQUdDLE9BQU07Z0RBQU14QyxXQUFVOzBEQUFpRjs7Ozs7OzBEQUczRyw4REFBQ3VDO2dEQUFHQyxPQUFNO2dEQUFNeEMsV0FBVTswREFBaUY7Ozs7Ozs0Q0FHekduRixDQUFBQSxlQUFlLFFBQVFBLGVBQWUsS0FBSSxtQkFDMUMsOERBQUMwSDtnREFBR0MsT0FBTTtnREFBTXhDLFdBQVU7MERBQWlGOzs7Ozs7MERBSTdHLDhEQUFDdUM7Z0RBQUdDLE9BQU07Z0RBQU14QyxXQUFVOzBEQUN4Qiw0RUFBQ1k7b0RBQUtaLFdBQVU7OERBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSWhDLDhEQUFDeUM7b0NBQU16QyxXQUFVOzhDQUdkcEYsUUFBUThGLEdBQUcsQ0FBQyxDQUFDaEIsdUJBQ1osOERBQUM0Qzs0Q0FFQ3RDLFdBQVU7OzhEQUdWLDhEQUFDMEM7b0RBQUcxQyxXQUFVOzhEQUNaLDRFQUFDRDt3REFBSUMsV0FBVTtrRUFDWmhCLGlCQUFpQlUsT0FBT3RELElBQUk7Ozs7Ozs7Ozs7OzhEQUtqQyw4REFBQ3NHO29EQUFHMUMsV0FBVTs4REFDWiw0RUFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDWk4sT0FBT2lELE9BQU87Ozs7OzswRUFFakIsOERBQUN2QztnRUFDQ0MsU0FBUyxJQUFNbEIsZ0JBQWdCTyxPQUFPaUQsT0FBTztnRUFDN0MzQyxXQUFVO2dFQUNWNEMsT0FBTTswRUFFTiw0RUFBQzVJLDJLQUFJQTtvRUFBQ2dHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBTXRCLDhEQUFDMEM7b0RBQUcxQyxXQUFVOzhEQUNaLDRFQUFDWTt3REFBS1osV0FBVTs7MEVBQ2QsOERBQUM3RiwyS0FBS0E7Z0VBQUM2RixXQUFVOzs7Ozs7NERBQ2hCN0IsVUFBVXVCLE9BQU90QixHQUFHOzs7Ozs7Ozs7Ozs7Z0RBS3ZCdkQsQ0FBQUEsZUFBZSxRQUFRQSxlQUFlLEtBQUksbUJBQzFDLDhEQUFDNkg7b0RBQUcxQyxXQUFVOzhEQUNaLDRFQUFDRDt3REFBSUMsV0FBVTtrRUFDWk4sT0FBT21ELFFBQVEsSUFBSTs7Ozs7Ozs7Ozs7OERBTTFCLDhEQUFDSDtvREFBRzFDLFdBQVU7OERBQ1osNEVBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0k7Z0VBQ0NDLFNBQVMsSUFBTXZGLFVBQVVBLE9BQU80RTtnRUFDaENNLFdBQVU7Z0VBQ1Y0QyxPQUFNOzBFQUVOLDRFQUFDOUksMktBQUlBO29FQUFDa0csV0FBVTs7Ozs7Ozs7Ozs7MEVBRWxCLDhEQUFDSTtnRUFDQ0MsU0FBUyxJQUFNWixrQkFBa0JDO2dFQUNqQ00sV0FBVTtnRUFDVjRDLE9BQU07MEVBRU4sNEVBQUM3SSwyS0FBTUE7b0VBQUNpRyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQ0ExRG5CTixPQUFPRSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUF1RTNCekUsK0JBQ0MsOERBQUM0RTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQzlGLDJLQUFhQTs0Q0FBQzhGLFdBQVU7Ozs7Ozs7Ozs7O2tEQUUzQiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNDOzRDQUFHRCxXQUFVO3NEQUE4Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS2hFLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUFFRixXQUFVOzs0Q0FBNkI7NENBQ0Y3RSwwQkFBQUEsb0NBQUFBLGNBQWVXLElBQUk7NENBQUM7Ozs7Ozs7a0RBRTVELDhEQUFDaUU7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ1poQixpQkFBaUI3RCwwQkFBQUEsb0NBQUFBLGNBQWVpQixJQUFJOzs7Ozs7OERBRXZDLDhEQUFDMkQ7b0RBQUlDLFdBQVU7OERBQ1o3RSwwQkFBQUEsb0NBQUFBLGNBQWV3SCxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLL0IsOERBQUM1QztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNJO3dDQUNDQyxTQUFTLElBQU1qRixpQkFBaUI7d0NBQ2hDNEUsV0FBVTtrREFDWDs7Ozs7O2tEQUdELDhEQUFDSTt3Q0FDQ0MsU0FBU1Y7d0NBQ1RLLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVqQjtHQXZsQndCckY7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvZG9tYWlucy9EbnNSZWNvcmRUYWJsZS5qc3g/YjJkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7XG4gIEdsb2JlLFxuICBMaW5rLFxuICBNYWlsLFxuICBGaWxlVGV4dCxcbiAgU2VydmVyLFxuICBTZXR0aW5ncyxcbiAgRWRpdCxcbiAgVHJhc2gyLFxuICBDb3B5LFxuICBQbHVzLFxuICBBbGVydFRyaWFuZ2xlLFxuICBDbG9jayxcbiAgU2F2ZSxcbiAgWCxcbiAgSW5mbyxcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IHtcbiAgVFRMX09QVElPTlMsXG4gIEROU19SRUNPUkRfVFlQRVMsXG4gIERFRkFVTFRfVFRMLFxuICB2YWxpZGF0ZURuc1JlY29yZFxufSBmcm9tIFwiQC9jb25zdGFudHMvZG5zUmVjb3Jkc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEbnNSZWNvcmRUYWJsZSh7XG4gIHJlY29yZHMsXG4gIHJlY29yZFR5cGUsXG4gIG9uRWRpdCxcbiAgb25EZWxldGUsXG4gIG9uQWRkLFxuICBkb21haW4sXG4gIGxvYWRpbmcgPSBmYWxzZSxcbn0pIHtcbiAgY29uc3QgW2RlbGV0ZUNvbmZpcm0sIHNldERlbGV0ZUNvbmZpcm1dID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtzaG93SW5saW5lRm9ybSwgc2V0U2hvd0lubGluZUZvcm1dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHt9KTtcbiAgY29uc3QgW2Zvcm1FcnJvcnMsIHNldEZvcm1FcnJvcnNdID0gdXNlU3RhdGUoe30pO1xuICBjb25zdCBbaXNTdWJtaXR0aW5nLCBzZXRJc1N1Ym1pdHRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIFJlc2V0IGZvcm0gc3RhdGUgd2hlbiByZWNvcmQgdHlwZSBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0U2hvd0lubGluZUZvcm0oZmFsc2UpO1xuICAgIHNldEZvcm1EYXRhKHt9KTtcbiAgICBzZXRGb3JtRXJyb3JzKHt9KTtcbiAgfSwgW3JlY29yZFR5cGVdKTtcblxuICAvLyBJbml0aWFsaXplIGZvcm0gZGF0YSBmb3IgdGhlIGN1cnJlbnQgcmVjb3JkIHR5cGVcbiAgY29uc3QgaW5pdGlhbGl6ZUZvcm1EYXRhID0gKHR5cGUpID0+IHtcbiAgICBjb25zdCByZWNvcmRUeXBlQ29uZmlnID0gRE5TX1JFQ09SRF9UWVBFU1t0eXBlXTtcbiAgICBpZiAoIXJlY29yZFR5cGVDb25maWcpIHJldHVybiB7IHR5cGUgfTtcblxuICAgIGNvbnN0IGluaXRpYWxEYXRhID0geyB0eXBlIH07XG4gICAgcmVjb3JkVHlwZUNvbmZpZy5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7XG4gICAgICBpZiAoZmllbGQubmFtZSA9PT0gJ3R0bCcpIHtcbiAgICAgICAgaW5pdGlhbERhdGFbZmllbGQubmFtZV0gPSBmaWVsZC5kZWZhdWx0IHx8IERFRkFVTFRfVFRMO1xuICAgICAgfSBlbHNlIGlmIChmaWVsZC50eXBlID09PSAnbnVtYmVyJykge1xuICAgICAgICBpbml0aWFsRGF0YVtmaWVsZC5uYW1lXSA9IGZpZWxkLmRlZmF1bHQgfHwgMDtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGluaXRpYWxEYXRhW2ZpZWxkLm5hbWVdID0gZmllbGQuZGVmYXVsdCB8fCAnJztcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIHJldHVybiBpbml0aWFsRGF0YTtcbiAgfTtcblxuICAvLyBIYW5kbGUgYWRkIGJ1dHRvbiBjbGljayAtIHNob3dzIGlubGluZSBmb3JtXG4gIGNvbnN0IGhhbmRsZUFkZENsaWNrID0gKCkgPT4ge1xuICAgIGNvbnN0IGluaXRpYWxEYXRhID0gaW5pdGlhbGl6ZUZvcm1EYXRhKHJlY29yZFR5cGUpO1xuICAgIHNldEZvcm1EYXRhKGluaXRpYWxEYXRhKTtcbiAgICBzZXRGb3JtRXJyb3JzKHt9KTtcbiAgICBzZXRTaG93SW5saW5lRm9ybSh0cnVlKTtcbiAgfTtcblxuICAvLyBIYW5kbGUgZm9ybSBmaWVsZCBjaGFuZ2VzXG4gIGNvbnN0IGhhbmRsZUZpZWxkQ2hhbmdlID0gKGZpZWxkTmFtZSwgdmFsdWUpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgW2ZpZWxkTmFtZV06IHZhbHVlXG4gICAgfSkpO1xuXG4gICAgLy8gQ2xlYXIgZXJyb3IgZm9yIHRoaXMgZmllbGRcbiAgICBpZiAoZm9ybUVycm9yc1tmaWVsZE5hbWVdKSB7XG4gICAgICBzZXRGb3JtRXJyb3JzKHByZXYgPT4gKHtcbiAgICAgICAgLi4ucHJldixcbiAgICAgICAgW2ZpZWxkTmFtZV06IG51bGxcbiAgICAgIH0pKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIGZvcm0gc3VibWlzc2lvblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZSkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRJc1N1Ym1pdHRpbmcodHJ1ZSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gVmFsaWRhdGUgdGhlIGZvcm0gZGF0YVxuICAgICAgY29uc3QgdmFsaWRhdGlvbiA9IHZhbGlkYXRlRG5zUmVjb3JkKGZvcm1EYXRhKTtcbiAgICAgIGlmICghdmFsaWRhdGlvbi5pc1ZhbGlkKSB7XG4gICAgICAgIHNldEZvcm1FcnJvcnModmFsaWRhdGlvbi5lcnJvcnMpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGF3YWl0IG9uQWRkKGZvcm1EYXRhKTtcbiAgICAgIHNldFNob3dJbmxpbmVGb3JtKGZhbHNlKTtcbiAgICAgIHNldEZvcm1EYXRhKHt9KTtcbiAgICAgIHNldEZvcm1FcnJvcnMoe30pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhZGRpbmcgcmVjb3JkOicsIGVycm9yKTtcbiAgICAgIC8vIEhhbmRsZSB2YWxpZGF0aW9uIGVycm9ycyBpZiBuZWVkZWRcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNTdWJtaXR0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIGZvcm0gY2FuY2VsXG4gIGNvbnN0IGhhbmRsZUNhbmNlbCA9ICgpID0+IHtcbiAgICBzZXRTaG93SW5saW5lRm9ybShmYWxzZSk7XG4gICAgc2V0Rm9ybURhdGEoe30pO1xuICAgIHNldEZvcm1FcnJvcnMoe30pO1xuICB9O1xuXG5cblxuICAvLyBHZXQgcmVjb3JkIHR5cGUgY29uZmlndXJhdGlvblxuICBjb25zdCBnZXRSZWNvcmRUeXBlQ29uZmlnID0gKHR5cGUpID0+IHtcbiAgICBjb25zdCBjb25maWdzID0ge1xuICAgICAgQToge1xuICAgICAgICBpY29uOiBHbG9iZSxcbiAgICAgICAgbGFiZWw6IFwiSVB2NCBBZGRyZXNzXCIsXG4gICAgICAgIGNvbG9yOiBcImJsdWVcIixcbiAgICAgICAgYmdDb2xvcjogXCJiZy1ibHVlLTUwXCIsXG4gICAgICAgIHRleHRDb2xvcjogXCJ0ZXh0LWJsdWUtNjAwXCIsXG4gICAgICAgIGJvcmRlckNvbG9yOiBcImJvcmRlci1ibHVlLTIwMFwiLFxuICAgICAgfSxcbiAgICAgIEFBQUE6IHtcbiAgICAgICAgaWNvbjogR2xvYmUsXG4gICAgICAgIGxhYmVsOiBcIklQdjYgQWRkcmVzc1wiLFxuICAgICAgICBjb2xvcjogXCJwdXJwbGVcIixcbiAgICAgICAgYmdDb2xvcjogXCJiZy1wdXJwbGUtNTBcIixcbiAgICAgICAgdGV4dENvbG9yOiBcInRleHQtcHVycGxlLTYwMFwiLFxuICAgICAgICBib3JkZXJDb2xvcjogXCJib3JkZXItcHVycGxlLTIwMFwiLFxuICAgICAgfSxcbiAgICAgIENOQU1FOiB7XG4gICAgICAgIGljb246IExpbmssXG4gICAgICAgIGxhYmVsOiBcIkNhbm9uaWNhbCBOYW1lXCIsXG4gICAgICAgIGNvbG9yOiBcImdyZWVuXCIsXG4gICAgICAgIGJnQ29sb3I6IFwiYmctZ3JlZW4tNTBcIixcbiAgICAgICAgdGV4dENvbG9yOiBcInRleHQtZ3JlZW4tNjAwXCIsXG4gICAgICAgIGJvcmRlckNvbG9yOiBcImJvcmRlci1ncmVlbi0yMDBcIixcbiAgICAgIH0sXG4gICAgICBNWDoge1xuICAgICAgICBpY29uOiBNYWlsLFxuICAgICAgICBsYWJlbDogXCJNYWlsIEV4Y2hhbmdlXCIsXG4gICAgICAgIGNvbG9yOiBcIm9yYW5nZVwiLFxuICAgICAgICBiZ0NvbG9yOiBcImJnLW9yYW5nZS01MFwiLFxuICAgICAgICB0ZXh0Q29sb3I6IFwidGV4dC1vcmFuZ2UtNjAwXCIsXG4gICAgICAgIGJvcmRlckNvbG9yOiBcImJvcmRlci1vcmFuZ2UtMjAwXCIsXG4gICAgICB9LFxuICAgICAgVFhUOiB7XG4gICAgICAgIGljb246IEZpbGVUZXh0LFxuICAgICAgICBsYWJlbDogXCJUZXh0IFJlY29yZFwiLFxuICAgICAgICBjb2xvcjogXCJncmF5XCIsXG4gICAgICAgIGJnQ29sb3I6IFwiYmctZ3JheS01MFwiLFxuICAgICAgICB0ZXh0Q29sb3I6IFwidGV4dC1ncmF5LTYwMFwiLFxuICAgICAgICBib3JkZXJDb2xvcjogXCJib3JkZXItZ3JheS0yMDBcIixcbiAgICAgIH0sXG4gICAgICBOUzoge1xuICAgICAgICBpY29uOiBTZXJ2ZXIsXG4gICAgICAgIGxhYmVsOiBcIk5hbWUgU2VydmVyXCIsXG4gICAgICAgIGNvbG9yOiBcImluZGlnb1wiLFxuICAgICAgICBiZ0NvbG9yOiBcImJnLWluZGlnby01MFwiLFxuICAgICAgICB0ZXh0Q29sb3I6IFwidGV4dC1pbmRpZ28tNjAwXCIsXG4gICAgICAgIGJvcmRlckNvbG9yOiBcImJvcmRlci1pbmRpZ28tMjAwXCIsXG4gICAgICB9LFxuICAgICAgU1JWOiB7XG4gICAgICAgIGljb246IFNldHRpbmdzLFxuICAgICAgICBsYWJlbDogXCJTZXJ2aWNlIFJlY29yZFwiLFxuICAgICAgICBjb2xvcjogXCJwaW5rXCIsXG4gICAgICAgIGJnQ29sb3I6IFwiYmctcGluay01MFwiLFxuICAgICAgICB0ZXh0Q29sb3I6IFwidGV4dC1waW5rLTYwMFwiLFxuICAgICAgICBib3JkZXJDb2xvcjogXCJib3JkZXItcGluay0yMDBcIixcbiAgICAgIH0sXG4gICAgfTtcbiAgICByZXR1cm4gY29uZmlnc1t0eXBlXSB8fCBjb25maWdzLkE7XG4gIH07XG5cbiAgLy8gRm9ybWF0IFRUTCBkaXNwbGF5XG4gIGNvbnN0IGZvcm1hdFRUTCA9ICh0dGwpID0+IHtcbiAgICBjb25zdCBudW1lcmljVFRMID0gcGFyc2VJbnQodHRsKTtcbiAgICBjb25zdCBvcHRpb24gPSBUVExfT1BUSU9OUy5maW5kKChvcHQpID0+IG9wdC52YWx1ZSA9PT0gbnVtZXJpY1RUTCk7XG4gICAgaWYgKG9wdGlvbikge1xuICAgICAgcmV0dXJuIG9wdGlvbi5sYWJlbDtcbiAgICB9XG4gICAgXG4gICAgLy8gRm9ybWF0IGN1c3RvbSBUVEwgdmFsdWVzXG4gICAgaWYgKG51bWVyaWNUVEwgPj0gODY0MDApIHtcbiAgICAgIGNvbnN0IGRheXMgPSBNYXRoLmZsb29yKG51bWVyaWNUVEwgLyA4NjQwMCk7XG4gICAgICBjb25zdCByZW1haW5kZXIgPSBudW1lcmljVFRMICUgODY0MDA7XG4gICAgICBpZiAocmVtYWluZGVyID09PSAwKSB7XG4gICAgICAgIHJldHVybiBgJHtkYXlzfSBkYXkke2RheXMgIT09IDEgPyAncycgOiAnJ31gO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAobnVtZXJpY1RUTCA+PSAzNjAwKSB7XG4gICAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3IobnVtZXJpY1RUTCAvIDM2MDApO1xuICAgICAgY29uc3QgcmVtYWluZGVyID0gbnVtZXJpY1RUTCAlIDM2MDA7XG4gICAgICBpZiAocmVtYWluZGVyID09PSAwKSB7XG4gICAgICAgIHJldHVybiBgJHtob3Vyc30gaG91ciR7aG91cnMgIT09IDEgPyAncycgOiAnJ31gO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAobnVtZXJpY1RUTCA+PSA2MCkge1xuICAgICAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3IobnVtZXJpY1RUTCAvIDYwKTtcbiAgICAgIGNvbnN0IHJlbWFpbmRlciA9IG51bWVyaWNUVEwgJSA2MDtcbiAgICAgIGlmIChyZW1haW5kZXIgPT09IDApIHtcbiAgICAgICAgcmV0dXJuIGAke21pbnV0ZXN9IG1pbnV0ZSR7bWludXRlcyAhPT0gMSA/ICdzJyA6ICcnfWA7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBgJHtudW1lcmljVFRMfXNgO1xuICB9O1xuXG4gIC8vIEZvcm1hdCByZWNvcmQgbmFtZVxuICBjb25zdCBmb3JtYXRSZWNvcmROYW1lID0gKG5hbWUpID0+IHtcbiAgICBpZiAoIW5hbWUgfHwgbmFtZSA9PT0gXCJAXCIpIHJldHVybiBkb21haW4/Lm5hbWUgfHwgXCJAXCI7XG4gICAgaWYgKG5hbWUuZW5kc1dpdGgoXCIuXCIpKSByZXR1cm4gbmFtZS5zbGljZSgwLCAtMSk7XG4gICAgcmV0dXJuIG5hbWU7XG4gIH07XG5cbiAgLy8gQ29weSB0byBjbGlwYm9hcmRcbiAgY29uc3QgY29weVRvQ2xpcGJvYXJkID0gYXN5bmMgKHRleHQpID0+IHtcbiAgICB0cnkge1xuICAgICAgYXdhaXQgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQodGV4dCk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGNvcHk6XCIsIGVycik7XG4gICAgfVxuICB9O1xuXG4gIC8vIEhhbmRsZSBkZWxldGUgY29uZmlybWF0aW9uXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUNsaWNrID0gKHJlY29yZCkgPT4ge1xuICAgIHNldERlbGV0ZUNvbmZpcm0ocmVjb3JkKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEZWxldGVDb25maXJtID0gKCkgPT4ge1xuICAgIGlmIChkZWxldGVDb25maXJtICYmIG9uRGVsZXRlKSB7XG4gICAgICBvbkRlbGV0ZShkZWxldGVDb25maXJtLmlkLCBkZWxldGVDb25maXJtKTtcbiAgICB9XG4gICAgc2V0RGVsZXRlQ29uZmlybShudWxsKTtcbiAgfTtcblxuICBjb25zdCBjb25maWcgPSBnZXRSZWNvcmRUeXBlQ29uZmlnKHJlY29yZFR5cGUpO1xuICBjb25zdCBJY29uQ29tcG9uZW50ID0gY29uZmlnLmljb247XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcC0yIHJvdW5kZWQtbGcgJHtjb25maWcuYmdDb2xvcn0gYm9yZGVyIGJvcmRlci1ncmF5LTIwMGB9PlxuICAgICAgICAgICAgICA8SWNvbkNvbXBvbmVudCBjbGFzc05hbWU9e2BoLTUgdy01ICR7Y29uZmlnLnRleHRDb2xvcn1gfSAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAge3JlY29yZFR5cGV9IFJlY29yZHNcbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAge2NvbmZpZy5sYWJlbH0g4oCiIHtyZWNvcmRzLmxlbmd0aH0gcmVjb3Jke3JlY29yZHMubGVuZ3RoICE9PSAxID8gJ3MnIDogJyd9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIHshc2hvd0lubGluZUZvcm0gJiYgKFxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBZGRDbGlja31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBiZy13aGl0ZSBob3ZlcjpiZy1ncmF5LTUwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgQWRkIHtyZWNvcmRUeXBlfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIElubGluZSBBZGQgRm9ybSAqL31cbiAgICAgICAge3Nob3dJbmxpbmVGb3JtICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICBBZGQge3JlY29yZFR5cGV9IFJlY29yZFxuICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICB7cmVjb3JkVHlwZSA9PT0gJ1NSVidcbiAgICAgICAgICAgICAgICAgICAgPyAnQ29uZmlndXJlIHNlcnZpY2UgbG9jYXRpb24gd2l0aCBwcmlvcml0eSwgd2VpZ2h0LCBwb3J0LCBhbmQgdGFyZ2V0IGhvc3QnXG4gICAgICAgICAgICAgICAgICAgIDogYEFkZCBhIG5ldyAke3JlY29yZFR5cGV9IHJlY29yZCBmb3IgJHtkb21haW4/Lm5hbWV9YFxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIER5bmFtaWMgRmllbGRzIGJhc2VkIG9uIHJlY29yZCB0eXBlICovfVxuICAgICAgICAgICAgICB7KCgpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCByZWNvcmRUeXBlQ29uZmlnID0gRE5TX1JFQ09SRF9UWVBFU1tyZWNvcmRUeXBlXTtcbiAgICAgICAgICAgICAgICBpZiAoIXJlY29yZFR5cGVDb25maWcpIHJldHVybiBudWxsO1xuXG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGRzID0gcmVjb3JkVHlwZUNvbmZpZy5maWVsZHM7XG4gICAgICAgICAgICAgICAgY29uc3QgZ3JpZENvbHMgPSBmaWVsZHMubGVuZ3RoIDw9IDMgPyBgbWQ6Z3JpZC1jb2xzLSR7ZmllbGRzLmxlbmd0aH1gIDogJ21kOmdyaWQtY29scy0zJztcblxuICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGdyaWQgZ3JpZC1jb2xzLTEgJHtncmlkQ29sc30gZ2FwLTRgfT5cbiAgICAgICAgICAgICAgICAgICAge2ZpZWxkcy5tYXAoKGZpZWxkKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2ZpZWxkLm5hbWV9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmaWVsZC5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2ZpZWxkLnJlcXVpcmVkICYmIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCBtbC0xXCI+Kjwvc3Bhbj59XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBBZGQgcmFuZ2UgaW5mbyBmb3IgU1JWIG51bWVyaWMgZmllbGRzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cmVjb3JkVHlwZSA9PT0gJ1NSVicgJiYgWydwcmlvcml0eScsICd3ZWlnaHQnLCAncG9ydCddLmluY2x1ZGVzKGZpZWxkLm5hbWUpICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbWwtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgW3tmaWVsZC5taW4gfHwgMH0te2ZpZWxkLm1heCB8fCA2NTUzNX1dXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIEFkZCBzcGVjaWZpYyBoZWxwIHRleHQgZm9yIFNSViBmaWVsZHMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICB7cmVjb3JkVHlwZSA9PT0gJ1NSVicgJiYgZmllbGQubmFtZSA9PT0gJ25hbWUnICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0yIHRleHQteHMgdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm1iLTFcIj5FeGFtcGxlczo8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+4oCiIF9jaGF0Ll90Y3Aue2RvbWFpbj8ubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+4oCiIF9zaXAuX3VkcC5zdWJkb21haW4ue2RvbWFpbj8ubmFtZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICAgICAge3JlY29yZFR5cGUgPT09ICdTUlYnICYmIGZpZWxkLm5hbWUgPT09ICdjb250ZW50JyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMiB0ZXh0LXhzIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD5UeXBlIGluIGEgZnVsbHkgcXVhbGlmaWVkIGRvbWFpbiBuYW1lIChlLmcuLCBhYmMucHFyLmNvbSk8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmaWVsZC50eXBlID09PSAnc2VsZWN0JyAmJiBmaWVsZC5uYW1lID09PSAndHRsJyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGFbZmllbGQubmFtZV0gfHwgZmllbGQuZGVmYXVsdCB8fCBERUZBVUxUX1RUTH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlRmllbGRDaGFuZ2UoZmllbGQubmFtZSwgcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtUVExfT1BUSU9OUy5tYXAoKG9wdGlvbikgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17b3B0aW9uLnZhbHVlfSB2YWx1ZT17b3B0aW9uLnZhbHVlfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7b3B0aW9uLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtyZWNvcmRUeXBlID09PSAnU1JWJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBEZWZhdWx0OiB7Zm9ybWF0VFRMKGZvcm1EYXRhW2ZpZWxkLm5hbWVdIHx8IGZpZWxkLmRlZmF1bHQgfHwgREVGQVVMVF9UVEwpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IGZpZWxkLnR5cGUgPT09ICdudW1iZXInID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGFbZmllbGQubmFtZV0gfHwgKHJlY29yZFR5cGUgPT09ICdTUlYnICYmIFsncHJpb3JpdHknLCAnd2VpZ2h0JywgJ3BvcnQnXS5pbmNsdWRlcyhmaWVsZC5uYW1lKSA/IDAgOiAnJyl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGaWVsZENoYW5nZShmaWVsZC5uYW1lLCBwYXJzZUludChlLnRhcmdldC52YWx1ZSkgfHwgMCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3JlY29yZFR5cGUgPT09ICdTUlYnICYmIGZpZWxkLm5hbWUgPT09ICdwb3J0JyA/ICc4MCwgNDQzLCA1MDYwLCBldGMuJyA6IGZpZWxkLnBsYWNlaG9sZGVyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbj17ZmllbGQubWlufVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1heD17ZmllbGQubWF4fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IGZpZWxkLnR5cGUgPT09ICd0ZXh0YXJlYScgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YVtmaWVsZC5uYW1lXSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpZWxkQ2hhbmdlKGZpZWxkLm5hbWUsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17ZmllbGQucGxhY2Vob2xkZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcm93cz17Mn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGFbZmllbGQubmFtZV0gfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGaWVsZENoYW5nZShmaWVsZC5uYW1lLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVjb3JkVHlwZSA9PT0gJ1NSVicgJiYgZmllbGQubmFtZSA9PT0gJ25hbWUnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gYF9zZXJ2aWNlLl9wcm90b2NvbCAoZS5nLiwgX3NpcC5fdGNwKWBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiByZWNvcmRUeXBlID09PSAnU1JWJyAmJiBmaWVsZC5uYW1lID09PSAnY29udGVudCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgdGFyZ2V0LiR7ZG9tYWluPy5uYW1lIHx8ICdleGFtcGxlLmNvbSd9YFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGZpZWxkLnBsYWNlaG9sZGVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtRXJyb3JzW2ZpZWxkLm5hbWVdICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPntmb3JtRXJyb3JzW2ZpZWxkLm5hbWVdfTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgfSkoKX1cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwdC00IGJvcmRlci10IGJvcmRlci1ibHVlLTIwMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNzAwXCI+XG4gICAgICAgICAgICAgICAgICB7cmVjb3JkVHlwZSA9PT0gJ1NSVicgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIFNlcnZpY2UgbmFtZSBmb3JtYXQ6IF9zZXJ2aWNlLl9wcm90b2NvbC57ZG9tYWluPy5uYW1lfSDigKIgVGFyZ2V0OiBmdWxseSBxdWFsaWZpZWQgZG9tYWluIG5hbWVcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgVXNlIFwiQFwiIGZvciByb290IGRvbWFpbiAoe2RvbWFpbj8ubmFtZX0pLCBcInd3d1wiIGZvciB3d3cue2RvbWFpbj8ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTNcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNhbmNlbH1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzU3VibWl0dGluZ31cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JheS01MCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1ibHVlLTUwMCBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwiaC00IHctNCBtci0yIGlubGluZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIGJnLWJsdWUtNjAwIGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgcm91bmRlZC1tZCBob3ZlcjpiZy1ibHVlLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1ibHVlLTUwMCBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2lzU3VibWl0dGluZyA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gaC00IHctNCBtci0yIGlubGluZVwiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Y2lyY2xlIGNsYXNzTmFtZT1cIm9wYWNpdHktMjVcIiBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIxMFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiNFwiPjwvY2lyY2xlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBjbGFzc05hbWU9XCJvcGFjaXR5LTc1XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIGQ9XCJNNCAxMmE4IDggMCAwMTgtOFYwQzUuMzczIDAgMCA1LjM3MyAwIDEyaDR6bTIgNS4yOTFBNy45NjIgNy45NjIgMCAwMTQgMTJIMGMwIDMuMDQyIDEuMTM1IDUuODI0IDMgNy45MzhsMy0yLjY0N3pcIj48L3BhdGg+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgICAgICAgIEFkZGluZy4uLlxuICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U2F2ZSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTIgaW5saW5lXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIEFkZCBSZWNvcmRcbiAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZm9ybT5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogVGFibGUgQ29udGVudCAqL31cbiAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGZvbnQtc2VtaWJvbGQgbGVhZGluZy02IHRleHQtc20gc2hhZG93IHJvdW5kZWQtbWQgdGV4dC1ncmF5LTUwMCBiZy13aGl0ZVwiPlxuICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiAtbWwtMSBtci0zIGgtNSB3LTUgdGV4dC1ncmF5LTUwMFwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICA8Y2lyY2xlIGNsYXNzTmFtZT1cIm9wYWNpdHktMjVcIiBjeD1cIjEyXCIgY3k9XCIxMlwiIHI9XCIxMFwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHN0cm9rZVdpZHRoPVwiNFwiPjwvY2lyY2xlPlxuICAgICAgICAgICAgICAgIDxwYXRoIGNsYXNzTmFtZT1cIm9wYWNpdHktNzVcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgZD1cIk00IDEyYTggOCAwIDAxOC04VjBDNS4zNzMgMCAwIDUuMzczIDAgMTJoNHptMiA1LjI5MUE3Ljk2MiA3Ljk2MiAwIDAxNCAxMkgwYzAgMy4wNDIgMS4xMzUgNS44MjQgMyA3LjkzOGwzLTIuNjQ3elwiPjwvcGF0aD5cbiAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIExvYWRpbmcge3JlY29yZFR5cGV9IHJlY29yZHMuLi5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogcmVjb3Jkcy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHAtNCByb3VuZGVkLWZ1bGwgJHtjb25maWcuYmdDb2xvcn1gfT5cbiAgICAgICAgICAgICAgICA8SWNvbkNvbXBvbmVudCBjbGFzc05hbWU9e2BoLTggdy04ICR7Y29uZmlnLnRleHRDb2xvcn1gfSAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIE5vIHtyZWNvcmRUeXBlfSByZWNvcmRzIGZvdW5kXG4gICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIHRleHQtc20gbWItNFwiPlxuICAgICAgICAgICAgICAgICAgQ3JlYXRlIHlvdXIgZmlyc3Qge3JlY29yZFR5cGV9IHJlY29yZCB0byBnZXQgc3RhcnRlZFxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBZGRDbGlja31cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgYm9yZGVyIGJvcmRlci10cmFuc3BhcmVudCByb3VuZGVkLW1kIHNoYWRvdy1zbSB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgYmctYmx1ZS02MDAgaG92ZXI6YmctYmx1ZS03MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgICBBZGQge3JlY29yZFR5cGV9IFJlY29yZFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwib3ZlcmZsb3ctaGlkZGVuIHNoYWRvdyByaW5nLTEgcmluZy1ibGFjayByaW5nLW9wYWNpdHktNSByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtZ3JheS0zMDBcIj5cbiAgICAgICAgICAgICAgPHRoZWFkIGNsYXNzTmFtZT1cImJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICA8dGggc2NvcGU9XCJjb2xcIiBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgTmFtZVxuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBzY29wZT1cImNvbFwiIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICBDb250ZW50XG4gICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgPHRoIHNjb3BlPVwiY29sXCIgY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIFRUTFxuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICAgIHsocmVjb3JkVHlwZSA9PT0gJ01YJyB8fCByZWNvcmRUeXBlID09PSAnU1JWJykgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8dGggc2NvcGU9XCJjb2xcIiBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICBQcmlvcml0eVxuICAgICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDx0aCBzY29wZT1cImNvbFwiIGNsYXNzTmFtZT1cInJlbGF0aXZlIHB4LTYgcHktM1wiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzci1vbmx5XCI+QWN0aW9uczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgPC90aGVhZD5cbiAgICAgICAgICAgICAgPHRib2R5IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMFwiPlxuXG5cbiAgICAgICAgICAgICAgICB7cmVjb3Jkcy5tYXAoKHJlY29yZCkgPT4gKFxuICAgICAgICAgICAgICAgICAgPHRyXG4gICAgICAgICAgICAgICAgICAgIGtleT17cmVjb3JkLmlkfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3ZlcjpiZy1ncmF5LTUwIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTE1MFwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIHsvKiBOYW1lIENvbHVtbiAqL31cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UmVjb3JkTmFtZShyZWNvcmQubmFtZSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIENvbnRlbnQgQ29sdW1uICovfVxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbW9ubyB0ZXh0LWdyYXktNzAwIGZsZXgtMSBtaW4tdy0wIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtyZWNvcmQuY29udGVudH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBjb3B5VG9DbGlwYm9hcmQocmVjb3JkLmNvbnRlbnQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWdyYXktNjAwIHJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkNvcHkgdG8gY2xpcGJvYXJkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENvcHkgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cblxuICAgICAgICAgICAgICAgICAgICB7LyogVFRMIENvbHVtbiAqL31cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yLjUgcHktMC41IHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLWdyYXktMTAwIHRleHQtZ3JheS04MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDbG9jayBjbGFzc05hbWU9XCJoLTMgdy0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFRUTChyZWNvcmQudHRsKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG5cbiAgICAgICAgICAgICAgICAgICAgey8qIFByaW9yaXR5IENvbHVtbiAoZm9yIE1YIGFuZCBTUlYgcmVjb3JkcykgKi99XG4gICAgICAgICAgICAgICAgICAgIHsocmVjb3JkVHlwZSA9PT0gJ01YJyB8fCByZWNvcmRUeXBlID09PSAnU1JWJykgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtyZWNvcmQucHJpb3JpdHkgfHwgJy0nfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgICAgICB7LyogQWN0aW9ucyBDb2x1bW4gKi99XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1yaWdodCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uRWRpdCAmJiBvbkVkaXQocmVjb3JkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1ibHVlLTYwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWJsdWUtNTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkVkaXQgcmVjb3JkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEVkaXQgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRGVsZXRlQ2xpY2socmVjb3JkKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS00MDAgaG92ZXI6dGV4dC1yZWQtNjAwIHJvdW5kZWQtbWQgaG92ZXI6YmctcmVkLTUwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJEZWxldGUgcmVjb3JkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPC90cj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICAgIDwvdGFibGU+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIERlbGV0ZSBDb25maXJtYXRpb24gRGlhbG9nICovfVxuICAgICAge2RlbGV0ZUNvbmZpcm0gJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctZ3JheS02MDAgYmctb3BhY2l0eS01MCBvdmVyZmxvdy15LWF1dG8gaC1mdWxsIHctZnVsbCB6LTUwXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB0b3AtMjAgbXgtYXV0byBwLTUgYm9yZGVyIHctOTYgc2hhZG93LWxnIHJvdW5kZWQtbWQgYmctd2hpdGVcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIG1iLTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gZmxleC1zaHJpbmstMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBoLTEyIHctMTIgcm91bmRlZC1mdWxsIGJnLXJlZC0xMDBcIj5cbiAgICAgICAgICAgICAgICAgIDxBbGVydFRyaWFuZ2xlIGNsYXNzTmFtZT1cImgtNiB3LTYgdGV4dC1yZWQtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHNtOnRleHQtbGVmdFwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgbGVhZGluZy02IGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgRGVsZXRlIEROUyBSZWNvcmRcbiAgICAgICAgICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTJcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSB0aGlzIHtkZWxldGVDb25maXJtPy50eXBlfSByZWNvcmQ/IFRoaXMgYWN0aW9uIGNhbm5vdCBiZSB1bmRvbmUuXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0zIGJnLWdyYXktNTAgcm91bmRlZC1sZyBib3JkZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRSZWNvcmROYW1lKGRlbGV0ZUNvbmZpcm0/Lm5hbWUpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1vbm8gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtkZWxldGVDb25maXJtPy5jb250ZW50fVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0zIG10LTZcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXREZWxldGVDb25maXJtKG51bGwpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHB4LTQgcHktMiBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1ncmF5LTUwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVEZWxldGVDb25maXJtfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHB4LTQgcHktMiBiZy1yZWQtNjAwIGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgcm91bmRlZC1tZCBzaGFkb3ctc20gdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIGhvdmVyOmJnLXJlZC03MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctcmVkLTUwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgRGVsZXRlIFJlY29yZFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC8+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJHbG9iZSIsIkxpbmsiLCJNYWlsIiwiRmlsZVRleHQiLCJTZXJ2ZXIiLCJTZXR0aW5ncyIsIkVkaXQiLCJUcmFzaDIiLCJDb3B5IiwiUGx1cyIsIkFsZXJ0VHJpYW5nbGUiLCJDbG9jayIsIlNhdmUiLCJYIiwiSW5mbyIsIlRUTF9PUFRJT05TIiwiRE5TX1JFQ09SRF9UWVBFUyIsIkRFRkFVTFRfVFRMIiwidmFsaWRhdGVEbnNSZWNvcmQiLCJEbnNSZWNvcmRUYWJsZSIsInJlY29yZHMiLCJyZWNvcmRUeXBlIiwib25FZGl0Iiwib25EZWxldGUiLCJvbkFkZCIsImRvbWFpbiIsImxvYWRpbmciLCJkZWxldGVDb25maXJtIiwic2V0RGVsZXRlQ29uZmlybSIsInNob3dJbmxpbmVGb3JtIiwic2V0U2hvd0lubGluZUZvcm0iLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwiZm9ybUVycm9ycyIsInNldEZvcm1FcnJvcnMiLCJpc1N1Ym1pdHRpbmciLCJzZXRJc1N1Ym1pdHRpbmciLCJpbml0aWFsaXplRm9ybURhdGEiLCJ0eXBlIiwicmVjb3JkVHlwZUNvbmZpZyIsImluaXRpYWxEYXRhIiwiZmllbGRzIiwiZm9yRWFjaCIsImZpZWxkIiwibmFtZSIsImRlZmF1bHQiLCJoYW5kbGVBZGRDbGljayIsImhhbmRsZUZpZWxkQ2hhbmdlIiwiZmllbGROYW1lIiwidmFsdWUiLCJwcmV2IiwiaGFuZGxlU3VibWl0IiwiZSIsInByZXZlbnREZWZhdWx0IiwidmFsaWRhdGlvbiIsImlzVmFsaWQiLCJlcnJvcnMiLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVDYW5jZWwiLCJnZXRSZWNvcmRUeXBlQ29uZmlnIiwiY29uZmlncyIsIkEiLCJpY29uIiwibGFiZWwiLCJjb2xvciIsImJnQ29sb3IiLCJ0ZXh0Q29sb3IiLCJib3JkZXJDb2xvciIsIkFBQUEiLCJDTkFNRSIsIk1YIiwiVFhUIiwiTlMiLCJTUlYiLCJmb3JtYXRUVEwiLCJ0dGwiLCJudW1lcmljVFRMIiwicGFyc2VJbnQiLCJvcHRpb24iLCJmaW5kIiwib3B0IiwiZGF5cyIsIk1hdGgiLCJmbG9vciIsInJlbWFpbmRlciIsImhvdXJzIiwibWludXRlcyIsImZvcm1hdFJlY29yZE5hbWUiLCJlbmRzV2l0aCIsInNsaWNlIiwiY29weVRvQ2xpcGJvYXJkIiwidGV4dCIsIm5hdmlnYXRvciIsImNsaXBib2FyZCIsIndyaXRlVGV4dCIsImVyciIsImhhbmRsZURlbGV0ZUNsaWNrIiwicmVjb3JkIiwiaGFuZGxlRGVsZXRlQ29uZmlybSIsImlkIiwiY29uZmlnIiwiSWNvbkNvbXBvbmVudCIsImRpdiIsImNsYXNzTmFtZSIsImgzIiwicCIsImxlbmd0aCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJmb3JtIiwib25TdWJtaXQiLCJoNCIsImdyaWRDb2xzIiwibWFwIiwicmVxdWlyZWQiLCJzcGFuIiwiaW5jbHVkZXMiLCJtaW4iLCJtYXgiLCJzZWxlY3QiLCJvbkNoYW5nZSIsInRhcmdldCIsImlucHV0IiwicGxhY2Vob2xkZXIiLCJ0ZXh0YXJlYSIsInJvd3MiLCJkaXNhYmxlZCIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInBhdGgiLCJkIiwieG1sbnMiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInNjb3BlIiwidGJvZHkiLCJ0ZCIsImNvbnRlbnQiLCJ0aXRsZSIsInByaW9yaXR5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\n"));

/***/ })

});
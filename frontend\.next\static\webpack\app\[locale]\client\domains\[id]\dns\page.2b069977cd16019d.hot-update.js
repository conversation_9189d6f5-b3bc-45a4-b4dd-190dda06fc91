"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx":
/*!***************************************************!*\
  !*** ./src/components/domains/DnsRecordTable.jsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DnsRecordTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/dnsRecords */ \"(app-pages-browser)/./src/constants/dnsRecords.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DnsRecordTable(param) {\n    let { records, recordType, onEdit, onDelete, onAdd, domain, loading = false } = param;\n    _s();\n    const [deleteConfirm, setDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInlineForm, setShowInlineForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Reset form state when record type changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowInlineForm(false);\n        setFormData({});\n        setFormErrors({});\n    }, [\n        recordType\n    ]);\n    // Initialize form data for the current record type\n    const initializeFormData = (type)=>{\n        const recordTypeConfig = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DNS_RECORD_TYPES[type];\n        if (!recordTypeConfig) return {\n            type\n        };\n        const initialData = {\n            type\n        };\n        recordTypeConfig.fields.forEach((field)=>{\n            if (field.name === \"ttl\") {\n                initialData[field.name] = field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL;\n            } else if (field.type === \"number\") {\n                initialData[field.name] = field.default || 0;\n            } else {\n                initialData[field.name] = field.default || \"\";\n            }\n        });\n        return initialData;\n    };\n    // Handle add button click - shows inline form\n    const handleAddClick = ()=>{\n        const initialData = initializeFormData(recordType);\n        setFormData(initialData);\n        setFormErrors({});\n        setShowInlineForm(true);\n    };\n    // Handle form field changes\n    const handleFieldChange = (fieldName, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [fieldName]: value\n            }));\n        // Clear error for this field\n        if (formErrors[fieldName]) {\n            setFormErrors((prev)=>({\n                    ...prev,\n                    [fieldName]: null\n                }));\n        }\n    };\n    // Handle form submission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            // Validate the form data\n            const validation = (0,_constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.validateDnsRecord)(formData);\n            if (!validation.isValid) {\n                setFormErrors(validation.errors);\n                return;\n            }\n            await onAdd(formData);\n            setShowInlineForm(false);\n            setFormData({});\n            setFormErrors({});\n        } catch (error) {\n            console.error(\"Error adding record:\", error);\n        // Handle validation errors if needed\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Handle form cancel\n    const handleCancel = ()=>{\n        setShowInlineForm(false);\n        setFormData({});\n        setFormErrors({});\n    };\n    // Get record type configuration\n    const getRecordTypeConfig = (type)=>{\n        const configs = {\n            A: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                label: \"IPv4 Address\",\n                color: \"blue\",\n                bgColor: \"bg-blue-50\",\n                textColor: \"text-blue-600\",\n                borderColor: \"border-blue-200\"\n            },\n            AAAA: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                label: \"IPv6 Address\",\n                color: \"purple\",\n                bgColor: \"bg-purple-50\",\n                textColor: \"text-purple-600\",\n                borderColor: \"border-purple-200\"\n            },\n            CNAME: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                label: \"Canonical Name\",\n                color: \"green\",\n                bgColor: \"bg-green-50\",\n                textColor: \"text-green-600\",\n                borderColor: \"border-green-200\"\n            },\n            MX: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                label: \"Mail Exchange\",\n                color: \"orange\",\n                bgColor: \"bg-orange-50\",\n                textColor: \"text-orange-600\",\n                borderColor: \"border-orange-200\"\n            },\n            TXT: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                label: \"Text Record\",\n                color: \"gray\",\n                bgColor: \"bg-gray-50\",\n                textColor: \"text-gray-600\",\n                borderColor: \"border-gray-200\"\n            },\n            NS: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                label: \"Name Server\",\n                color: \"indigo\",\n                bgColor: \"bg-indigo-50\",\n                textColor: \"text-indigo-600\",\n                borderColor: \"border-indigo-200\"\n            },\n            SRV: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                label: \"Service Record\",\n                color: \"pink\",\n                bgColor: \"bg-pink-50\",\n                textColor: \"text-pink-600\",\n                borderColor: \"border-pink-200\"\n            }\n        };\n        return configs[type] || configs.A;\n    };\n    // Format TTL display\n    const formatTTL = (ttl)=>{\n        const numericTTL = parseInt(ttl);\n        const option = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.TTL_OPTIONS.find((opt)=>opt.value === numericTTL);\n        if (option) {\n            return option.label;\n        }\n        // Format custom TTL values\n        if (numericTTL >= 86400) {\n            const days = Math.floor(numericTTL / 86400);\n            const remainder = numericTTL % 86400;\n            if (remainder === 0) {\n                return \"\".concat(days, \" day\").concat(days !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 3600) {\n            const hours = Math.floor(numericTTL / 3600);\n            const remainder = numericTTL % 3600;\n            if (remainder === 0) {\n                return \"\".concat(hours, \" hour\").concat(hours !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 60) {\n            const minutes = Math.floor(numericTTL / 60);\n            const remainder = numericTTL % 60;\n            if (remainder === 0) {\n                return \"\".concat(minutes, \" minute\").concat(minutes !== 1 ? \"s\" : \"\");\n            }\n        }\n        return \"\".concat(numericTTL, \"s\");\n    };\n    // Format record name\n    const formatRecordName = (name)=>{\n        if (!name || name === \"@\") return (domain === null || domain === void 0 ? void 0 : domain.name) || \"@\";\n        if (name.endsWith(\".\")) return name.slice(0, -1);\n        return name;\n    };\n    // Copy to clipboard\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n        } catch (err) {\n            console.error(\"Failed to copy:\", err);\n        }\n    };\n    // Handle delete confirmation\n    const handleDeleteClick = (record)=>{\n        setDeleteConfirm(record);\n    };\n    const handleDeleteConfirm = ()=>{\n        if (deleteConfirm && onDelete) {\n            onDelete(deleteConfirm.id, deleteConfirm);\n        }\n        setDeleteConfirm(null);\n    };\n    const config = getRecordTypeConfig(recordType);\n    const IconComponent = config.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 rounded-lg \".concat(config.bgColor, \" border border-gray-200\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"h-5 w-5 \".concat(config.textColor)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: [\n                                                    recordType,\n                                                    \" Records\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    config.label,\n                                                    \" • \",\n                                                    records.length,\n                                                    \" record\",\n                                                    records.length !== 1 ? \"s\" : \"\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this),\n                            !showInlineForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAddClick,\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add \",\n                                    recordType\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    showInlineForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: [\n                                                \"Add \",\n                                                recordType,\n                                                \" Record\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: recordType === \"SRV\" ? \"Configure service location with priority, weight, port, and target host\" : \"Add a new \".concat(recordType, \" record for \").concat(domain === null || domain === void 0 ? void 0 : domain.name)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this),\n                                (()=>{\n                                    const recordTypeConfig = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DNS_RECORD_TYPES[recordType];\n                                    if (!recordTypeConfig) return null;\n                                    const fields = recordTypeConfig.fields;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: [\n                                                            field.label,\n                                                            field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 ml-1\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 46\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    field.type === \"select\" && field.name === \"ttl\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: formData[field.name] || field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL,\n                                                                onChange: (e)=>handleFieldChange(field.name, parseInt(e.target.value)),\n                                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                children: _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.TTL_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: option.value,\n                                                                        children: option.label\n                                                                    }, option.value, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 33\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            recordType === \"SRV\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"Default: \",\n                                                                    formatTTL(formData[field.name] || field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 27\n                                                    }, this) : field.type === \"number\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData[field.name] || (recordType === \"SRV\" && [\n                                                            \"priority\",\n                                                            \"weight\",\n                                                            \"port\"\n                                                        ].includes(field.name) ? 0 : \"\"),\n                                                        onChange: (e)=>handleFieldChange(field.name, parseInt(e.target.value) || 0),\n                                                        placeholder: recordType === \"SRV\" && field.name === \"port\" ? \"80, 443, 5060, etc.\" : field.placeholder,\n                                                        min: field.min,\n                                                        max: field.max,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 27\n                                                    }, this) : field.type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                                                        placeholder: field.placeholder,\n                                                        rows: 2,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                                                        placeholder: recordType === \"SRV\" && field.name === \"name\" ? \"_service._protocol (e.g., _sip._tcp)\" : recordType === \"SRV\" && field.name === \"content\" ? \"target.\".concat((domain === null || domain === void 0 ? void 0 : domain.name) || \"example.com\") : field.placeholder,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    formErrors[field.name] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: formErrors[field.name]\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 19\n                                    }, this);\n                                })(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between pt-4 border-t border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: recordType === \"SRV\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Service name format: _service._protocol.\",\n                                                    domain === null || domain === void 0 ? void 0 : domain.name,\n                                                    \" • Target: fully qualified domain name\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    'Use \"@\" for root domain (',\n                                                    domain === null || domain === void 0 ? void 0 : domain.name,\n                                                    '), \"www\" for www.',\n                                                    domain === null || domain === void 0 ? void 0 : domain.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleCancel,\n                                                    disabled: isSubmitting,\n                                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 inline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Cancel\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isSubmitting,\n                                                    className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"animate-spin h-4 w-4 mr-2 inline\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        className: \"opacity-25\",\n                                                                        cx: \"12\",\n                                                                        cy: \"12\",\n                                                                        r: \"10\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        className: \"opacity-75\",\n                                                                        fill: \"currentColor\",\n                                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Adding...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2 inline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Add Record\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 15\n                                }, this),\n                                \"Loading \",\n                                recordType,\n                                \" records...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 424,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this) : records.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 rounded-full \".concat(config.bgColor),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-8 w-8 \".concat(config.textColor)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: [\n                                                \"No \",\n                                                recordType,\n                                                \" records found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: [\n                                                \"Create your first \",\n                                                recordType,\n                                                \" record to get started\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddClick,\n                                            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add \",\n                                                recordType,\n                                                \" Record\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 434,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 433,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"TTL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this),\n                                            (recordType === \"MX\" || recordType === \"SRV\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Priority\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"relative px-6 py-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: records.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50 transition-colors duration-150\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: formatRecordName(record.name)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-mono text-gray-700 flex-1 min-w-0 truncate\",\n                                                                children: record.content\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>copyToClipboard(record.content),\n                                                                className: \"p-1 text-gray-400 hover:text-gray-600 rounded\",\n                                                                title: \"Copy to clipboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 505,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            formatTTL(record.ttl)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 21\n                                                }, this),\n                                                (recordType === \"MX\" || recordType === \"SRV\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-900\",\n                                                        children: record.priority || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>onEdit && onEdit(record),\n                                                                className: \"p-2 text-gray-400 hover:text-blue-600 rounded-md hover:bg-blue-50\",\n                                                                title: \"Edit record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDeleteClick(record),\n                                                                className: \"p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-red-50\",\n                                                                title: \"Delete record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, record.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 457,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            deleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-6 w-6 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center sm:text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg leading-6 font-medium text-gray-900\",\n                                            children: \"Delete DNS Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 559,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mb-4\",\n                                        children: [\n                                            \"Are you sure you want to delete this \",\n                                            deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.type,\n                                            \" record? This action cannot be undone.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-gray-50 rounded-lg border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                    children: formatRecordName(deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.name)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-mono text-gray-600\",\n                                                    children: deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.content\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 573,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 569,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDeleteConfirm(null),\n                                        className: \"flex-1 px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDeleteConfirm,\n                                        className: \"flex-1 px-4 py-2 bg-red-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                        children: \"Delete Record\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 584,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 558,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                    lineNumber: 557,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 556,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DnsRecordTable, \"EMPfo3qgCdukLUyhx/J9sXiisRk=\");\n_c = DnsRecordTable;\nvar _c;\n$RefreshReg$(_c, \"DnsRecordTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2RvbWFpbnMvRG5zUmVjb3JkVGFibGUuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM0QztBQWlCdEI7QUFNVTtBQUVqQixTQUFTcUIsZUFBZSxLQVF0QztRQVJzQyxFQUNyQ0MsT0FBTyxFQUNQQyxVQUFVLEVBQ1ZDLE1BQU0sRUFDTkMsUUFBUSxFQUNSQyxLQUFLLEVBQ0xDLE1BQU0sRUFDTkMsVUFBVSxLQUFLLEVBQ2hCLEdBUnNDOztJQVNyQyxNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHOUIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDK0IsZ0JBQWdCQyxrQkFBa0IsR0FBR2hDLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sQ0FBQ2lDLFVBQVVDLFlBQVksR0FBR2xDLCtDQUFRQSxDQUFDLENBQUM7SUFDMUMsTUFBTSxDQUFDbUMsWUFBWUMsY0FBYyxHQUFHcEMsK0NBQVFBLENBQUMsQ0FBQztJQUM5QyxNQUFNLENBQUNxQyxjQUFjQyxnQkFBZ0IsR0FBR3RDLCtDQUFRQSxDQUFDO0lBRWpELDRDQUE0QztJQUM1Q0MsZ0RBQVNBLENBQUM7UUFDUitCLGtCQUFrQjtRQUNsQkUsWUFBWSxDQUFDO1FBQ2JFLGNBQWMsQ0FBQztJQUNqQixHQUFHO1FBQUNiO0tBQVc7SUFFZixtREFBbUQ7SUFDbkQsTUFBTWdCLHFCQUFxQixDQUFDQztRQUMxQixNQUFNQyxtQkFBbUJ2QixtRUFBZ0IsQ0FBQ3NCLEtBQUs7UUFDL0MsSUFBSSxDQUFDQyxrQkFBa0IsT0FBTztZQUFFRDtRQUFLO1FBRXJDLE1BQU1FLGNBQWM7WUFBRUY7UUFBSztRQUMzQkMsaUJBQWlCRSxNQUFNLENBQUNDLE9BQU8sQ0FBQ0MsQ0FBQUE7WUFDOUIsSUFBSUEsTUFBTUMsSUFBSSxLQUFLLE9BQU87Z0JBQ3hCSixXQUFXLENBQUNHLE1BQU1DLElBQUksQ0FBQyxHQUFHRCxNQUFNRSxPQUFPLElBQUk1Qiw4REFBV0E7WUFDeEQsT0FBTyxJQUFJMEIsTUFBTUwsSUFBSSxLQUFLLFVBQVU7Z0JBQ2xDRSxXQUFXLENBQUNHLE1BQU1DLElBQUksQ0FBQyxHQUFHRCxNQUFNRSxPQUFPLElBQUk7WUFDN0MsT0FBTztnQkFDTEwsV0FBVyxDQUFDRyxNQUFNQyxJQUFJLENBQUMsR0FBR0QsTUFBTUUsT0FBTyxJQUFJO1lBQzdDO1FBQ0Y7UUFFQSxPQUFPTDtJQUNUO0lBRUEsOENBQThDO0lBQzlDLE1BQU1NLGlCQUFpQjtRQUNyQixNQUFNTixjQUFjSCxtQkFBbUJoQjtRQUN2Q1csWUFBWVE7UUFDWk4sY0FBYyxDQUFDO1FBQ2ZKLGtCQUFrQjtJQUNwQjtJQUVBLDRCQUE0QjtJQUM1QixNQUFNaUIsb0JBQW9CLENBQUNDLFdBQVdDO1FBQ3BDakIsWUFBWWtCLENBQUFBLE9BQVM7Z0JBQ25CLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ0YsVUFBVSxFQUFFQztZQUNmO1FBRUEsNkJBQTZCO1FBQzdCLElBQUloQixVQUFVLENBQUNlLFVBQVUsRUFBRTtZQUN6QmQsY0FBY2dCLENBQUFBLE9BQVM7b0JBQ3JCLEdBQUdBLElBQUk7b0JBQ1AsQ0FBQ0YsVUFBVSxFQUFFO2dCQUNmO1FBQ0Y7SUFDRjtJQUVBLHlCQUF5QjtJQUN6QixNQUFNRyxlQUFlLE9BQU9DO1FBQzFCQSxFQUFFQyxjQUFjO1FBQ2hCakIsZ0JBQWdCO1FBRWhCLElBQUk7WUFDRix5QkFBeUI7WUFDekIsTUFBTWtCLGFBQWFwQyx3RUFBaUJBLENBQUNhO1lBQ3JDLElBQUksQ0FBQ3VCLFdBQVdDLE9BQU8sRUFBRTtnQkFDdkJyQixjQUFjb0IsV0FBV0UsTUFBTTtnQkFDL0I7WUFDRjtZQUVBLE1BQU1oQyxNQUFNTztZQUNaRCxrQkFBa0I7WUFDbEJFLFlBQVksQ0FBQztZQUNiRSxjQUFjLENBQUM7UUFDakIsRUFBRSxPQUFPdUIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsd0JBQXdCQTtRQUN0QyxxQ0FBcUM7UUFDdkMsU0FBVTtZQUNSckIsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxxQkFBcUI7SUFDckIsTUFBTXVCLGVBQWU7UUFDbkI3QixrQkFBa0I7UUFDbEJFLFlBQVksQ0FBQztRQUNiRSxjQUFjLENBQUM7SUFDakI7SUFJQSxnQ0FBZ0M7SUFDaEMsTUFBTTBCLHNCQUFzQixDQUFDdEI7UUFDM0IsTUFBTXVCLFVBQVU7WUFDZEMsR0FBRztnQkFDREMsTUFBTS9ELDBLQUFLQTtnQkFDWGdFLE9BQU87Z0JBQ1BDLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLGFBQWE7WUFDZjtZQUNBQyxNQUFNO2dCQUNKTixNQUFNL0QsMEtBQUtBO2dCQUNYZ0UsT0FBTztnQkFDUEMsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsV0FBVztnQkFDWEMsYUFBYTtZQUNmO1lBQ0FFLE9BQU87Z0JBQ0xQLE1BQU05RCwwS0FBSUE7Z0JBQ1YrRCxPQUFPO2dCQUNQQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxXQUFXO2dCQUNYQyxhQUFhO1lBQ2Y7WUFDQUcsSUFBSTtnQkFDRlIsTUFBTTdELDBLQUFJQTtnQkFDVjhELE9BQU87Z0JBQ1BDLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLGFBQWE7WUFDZjtZQUNBSSxLQUFLO2dCQUNIVCxNQUFNNUQsMEtBQVFBO2dCQUNkNkQsT0FBTztnQkFDUEMsT0FBTztnQkFDUEMsU0FBUztnQkFDVEMsV0FBVztnQkFDWEMsYUFBYTtZQUNmO1lBQ0FLLElBQUk7Z0JBQ0ZWLE1BQU0zRCwwS0FBTUE7Z0JBQ1o0RCxPQUFPO2dCQUNQQyxPQUFPO2dCQUNQQyxTQUFTO2dCQUNUQyxXQUFXO2dCQUNYQyxhQUFhO1lBQ2Y7WUFDQU0sS0FBSztnQkFDSFgsTUFBTTFELDBLQUFRQTtnQkFDZDJELE9BQU87Z0JBQ1BDLE9BQU87Z0JBQ1BDLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLGFBQWE7WUFDZjtRQUNGO1FBQ0EsT0FBT1AsT0FBTyxDQUFDdkIsS0FBSyxJQUFJdUIsUUFBUUMsQ0FBQztJQUNuQztJQUVBLHFCQUFxQjtJQUNyQixNQUFNYSxZQUFZLENBQUNDO1FBQ2pCLE1BQU1DLGFBQWFDLFNBQVNGO1FBQzVCLE1BQU1HLFNBQVNoRSw4REFBV0EsQ0FBQ2lFLElBQUksQ0FBQyxDQUFDQyxNQUFRQSxJQUFJaEMsS0FBSyxLQUFLNEI7UUFDdkQsSUFBSUUsUUFBUTtZQUNWLE9BQU9BLE9BQU9mLEtBQUs7UUFDckI7UUFFQSwyQkFBMkI7UUFDM0IsSUFBSWEsY0FBYyxPQUFPO1lBQ3ZCLE1BQU1LLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ1AsYUFBYTtZQUNyQyxNQUFNUSxZQUFZUixhQUFhO1lBQy9CLElBQUlRLGNBQWMsR0FBRztnQkFDbkIsT0FBTyxHQUFjSCxPQUFYQSxNQUFLLFFBQTRCLE9BQXRCQSxTQUFTLElBQUksTUFBTTtZQUMxQztRQUNGLE9BQU8sSUFBSUwsY0FBYyxNQUFNO1lBQzdCLE1BQU1TLFFBQVFILEtBQUtDLEtBQUssQ0FBQ1AsYUFBYTtZQUN0QyxNQUFNUSxZQUFZUixhQUFhO1lBQy9CLElBQUlRLGNBQWMsR0FBRztnQkFDbkIsT0FBTyxHQUFnQkMsT0FBYkEsT0FBTSxTQUE4QixPQUF2QkEsVUFBVSxJQUFJLE1BQU07WUFDN0M7UUFDRixPQUFPLElBQUlULGNBQWMsSUFBSTtZQUMzQixNQUFNVSxVQUFVSixLQUFLQyxLQUFLLENBQUNQLGFBQWE7WUFDeEMsTUFBTVEsWUFBWVIsYUFBYTtZQUMvQixJQUFJUSxjQUFjLEdBQUc7Z0JBQ25CLE9BQU8sR0FBb0JFLE9BQWpCQSxTQUFRLFdBQWtDLE9BQXpCQSxZQUFZLElBQUksTUFBTTtZQUNuRDtRQUNGO1FBRUEsT0FBTyxHQUFjLE9BQVhWLFlBQVc7SUFDdkI7SUFFQSxxQkFBcUI7SUFDckIsTUFBTVcsbUJBQW1CLENBQUM1QztRQUN4QixJQUFJLENBQUNBLFFBQVFBLFNBQVMsS0FBSyxPQUFPbkIsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRbUIsSUFBSSxLQUFJO1FBQ2xELElBQUlBLEtBQUs2QyxRQUFRLENBQUMsTUFBTSxPQUFPN0MsS0FBSzhDLEtBQUssQ0FBQyxHQUFHLENBQUM7UUFDOUMsT0FBTzlDO0lBQ1Q7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTStDLGtCQUFrQixPQUFPQztRQUM3QixJQUFJO1lBQ0YsTUFBTUMsVUFBVUMsU0FBUyxDQUFDQyxTQUFTLENBQUNIO1FBQ3RDLEVBQUUsT0FBT0ksS0FBSztZQUNadEMsUUFBUUQsS0FBSyxDQUFDLG1CQUFtQnVDO1FBQ25DO0lBQ0Y7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTUMsb0JBQW9CLENBQUNDO1FBQ3pCdEUsaUJBQWlCc0U7SUFDbkI7SUFFQSxNQUFNQyxzQkFBc0I7UUFDMUIsSUFBSXhFLGlCQUFpQkosVUFBVTtZQUM3QkEsU0FBU0ksY0FBY3lFLEVBQUUsRUFBRXpFO1FBQzdCO1FBQ0FDLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU15RSxTQUFTekMsb0JBQW9CdkM7SUFDbkMsTUFBTWlGLGdCQUFnQkQsT0FBT3RDLElBQUk7SUFFakMscUJBQ0U7OzBCQUNFLDhEQUFDd0M7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVcsa0JBQWlDLE9BQWZILE9BQU9uQyxPQUFPLEVBQUM7a0RBQy9DLDRFQUFDb0M7NENBQWNFLFdBQVcsV0FBNEIsT0FBakJILE9BQU9sQyxTQUFTOzs7Ozs7Ozs7OztrREFFdkQsOERBQUNvQzs7MERBQ0MsOERBQUNFO2dEQUFHRCxXQUFVOztvREFDWG5GO29EQUFXOzs7Ozs7OzBEQUVkLDhEQUFDcUY7Z0RBQUVGLFdBQVU7O29EQUNWSCxPQUFPckMsS0FBSztvREFBQztvREFBSTVDLFFBQVF1RixNQUFNO29EQUFDO29EQUFRdkYsUUFBUXVGLE1BQU0sS0FBSyxJQUFJLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBSTNFLENBQUM5RSxnQ0FDQSw4REFBQytFO2dDQUNDQyxTQUFTL0Q7Z0NBQ1QwRCxXQUFVOztrREFFViw4REFBQy9GLDBLQUFJQTt3Q0FBQytGLFdBQVU7Ozs7OztvQ0FBaUI7b0NBQzVCbkY7Ozs7Ozs7Ozs7Ozs7b0JBTVZRLGdDQUNDLDhEQUFDMEU7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNNOzRCQUFLQyxVQUFVNUQ7NEJBQWNxRCxXQUFVOzs4Q0FDdEMsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ1E7NENBQUdSLFdBQVU7O2dEQUF5QztnREFDaERuRjtnREFBVzs7Ozs7OztzREFFbEIsOERBQUNxRjs0Q0FBRUYsV0FBVTtzREFDVm5GLGVBQWUsUUFDWiw0RUFDQSxvQkFBYUEsWUFBVyxnQkFBMkIsT0FBYkksbUJBQUFBLDZCQUFBQSxPQUFRbUIsSUFBSTs7Ozs7Ozs7Ozs7O2dDQU14RDtvQ0FDQSxNQUFNTCxtQkFBbUJ2QixtRUFBZ0IsQ0FBQ0ssV0FBVztvQ0FDckQsSUFBSSxDQUFDa0Isa0JBQWtCLE9BQU87b0NBRTlCLE1BQU1FLFNBQVNGLGlCQUFpQkUsTUFBTTtvQ0FFdEMscUJBQ0UsOERBQUM4RDt3Q0FBSUMsV0FBVTtrREFDWi9ELE9BQU93RSxHQUFHLENBQUMsQ0FBQ3RFLHNCQUNYLDhEQUFDNEQ7O2tFQUNDLDhEQUFDdkM7d0RBQU13QyxXQUFVOzs0REFDZDdELE1BQU1xQixLQUFLOzREQUNYckIsTUFBTXVFLFFBQVEsa0JBQUksOERBQUNDO2dFQUFLWCxXQUFVOzBFQUFvQjs7Ozs7Ozs7Ozs7O29EQUV4RDdELE1BQU1MLElBQUksS0FBSyxZQUFZSyxNQUFNQyxJQUFJLEtBQUssc0JBQ3pDLDhEQUFDMkQ7OzBFQUNDLDhEQUFDYTtnRUFDQ25FLE9BQU9sQixRQUFRLENBQUNZLE1BQU1DLElBQUksQ0FBQyxJQUFJRCxNQUFNRSxPQUFPLElBQUk1Qiw4REFBV0E7Z0VBQzNEb0csVUFBVSxDQUFDakUsSUFBTUwsa0JBQWtCSixNQUFNQyxJQUFJLEVBQUVrQyxTQUFTMUIsRUFBRWtFLE1BQU0sQ0FBQ3JFLEtBQUs7Z0VBQ3RFdUQsV0FBVTswRUFFVHpGLDhEQUFXQSxDQUFDa0csR0FBRyxDQUFDLENBQUNsQyx1QkFDaEIsOERBQUNBO3dFQUEwQjlCLE9BQU84QixPQUFPOUIsS0FBSztrRkFDM0M4QixPQUFPZixLQUFLO3VFQURGZSxPQUFPOUIsS0FBSzs7Ozs7Ozs7Ozs0REFLNUI1QixlQUFlLHVCQUNkLDhEQUFDcUY7Z0VBQUVGLFdBQVU7O29FQUE2QjtvRUFDOUI3QixVQUFVNUMsUUFBUSxDQUFDWSxNQUFNQyxJQUFJLENBQUMsSUFBSUQsTUFBTUUsT0FBTyxJQUFJNUIsOERBQVdBOzs7Ozs7Ozs7Ozs7K0RBSTVFMEIsTUFBTUwsSUFBSSxLQUFLLHlCQUNqQiw4REFBQ2lGO3dEQUNDakYsTUFBSzt3REFDTFcsT0FBT2xCLFFBQVEsQ0FBQ1ksTUFBTUMsSUFBSSxDQUFDLElBQUt2QixDQUFBQSxlQUFlLFNBQVM7NERBQUM7NERBQVk7NERBQVU7eURBQU8sQ0FBQ21HLFFBQVEsQ0FBQzdFLE1BQU1DLElBQUksSUFBSSxJQUFJLEVBQUM7d0RBQ25IeUUsVUFBVSxDQUFDakUsSUFBTUwsa0JBQWtCSixNQUFNQyxJQUFJLEVBQUVrQyxTQUFTMUIsRUFBRWtFLE1BQU0sQ0FBQ3JFLEtBQUssS0FBSzt3REFDM0V3RSxhQUFhcEcsZUFBZSxTQUFTc0IsTUFBTUMsSUFBSSxLQUFLLFNBQVMsd0JBQXdCRCxNQUFNOEUsV0FBVzt3REFDdEdDLEtBQUsvRSxNQUFNK0UsR0FBRzt3REFDZEMsS0FBS2hGLE1BQU1nRixHQUFHO3dEQUNkbkIsV0FBVTs7Ozs7K0RBRVY3RCxNQUFNTCxJQUFJLEtBQUssMkJBQ2pCLDhEQUFDc0Y7d0RBQ0MzRSxPQUFPbEIsUUFBUSxDQUFDWSxNQUFNQyxJQUFJLENBQUMsSUFBSTt3REFDL0J5RSxVQUFVLENBQUNqRSxJQUFNTCxrQkFBa0JKLE1BQU1DLElBQUksRUFBRVEsRUFBRWtFLE1BQU0sQ0FBQ3JFLEtBQUs7d0RBQzdEd0UsYUFBYTlFLE1BQU04RSxXQUFXO3dEQUM5QkksTUFBTTt3REFDTnJCLFdBQVU7Ozs7OzZFQUdaLDhEQUFDZTt3REFDQ2pGLE1BQUs7d0RBQ0xXLE9BQU9sQixRQUFRLENBQUNZLE1BQU1DLElBQUksQ0FBQyxJQUFJO3dEQUMvQnlFLFVBQVUsQ0FBQ2pFLElBQU1MLGtCQUFrQkosTUFBTUMsSUFBSSxFQUFFUSxFQUFFa0UsTUFBTSxDQUFDckUsS0FBSzt3REFDN0R3RSxhQUNFcEcsZUFBZSxTQUFTc0IsTUFBTUMsSUFBSSxLQUFLLFNBQ2xDLHlDQUNEdkIsZUFBZSxTQUFTc0IsTUFBTUMsSUFBSSxLQUFLLFlBQ3ZDLFVBQXdDLE9BQTlCbkIsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRbUIsSUFBSSxLQUFJLGlCQUMxQkQsTUFBTThFLFdBQVc7d0RBRXZCakIsV0FBVTs7Ozs7O29EQUdidkUsVUFBVSxDQUFDVSxNQUFNQyxJQUFJLENBQUMsa0JBQ3JCLDhEQUFDOEQ7d0RBQUVGLFdBQVU7a0VBQTZCdkUsVUFBVSxDQUFDVSxNQUFNQyxJQUFJLENBQUM7Ozs7Ozs7K0NBMUQxREQsTUFBTUMsSUFBSTs7Ozs7Ozs7OztnQ0FnRTVCOzhDQUVBLDhEQUFDMkQ7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDWm5GLGVBQWUsc0JBQ2QsOERBQUM4Rjs7b0RBQUs7b0RBQ3FDMUYsbUJBQUFBLDZCQUFBQSxPQUFRbUIsSUFBSTtvREFBQzs7Ozs7O3FFQUd4RCw4REFBQ3VFOztvREFBSztvREFDc0IxRixtQkFBQUEsNkJBQUFBLE9BQVFtQixJQUFJO29EQUFDO29EQUFrQm5CLG1CQUFBQSw2QkFBQUEsT0FBUW1CLElBQUk7Ozs7Ozs7Ozs7OztzREFJM0UsOERBQUMyRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNJO29EQUNDdEUsTUFBSztvREFDTHVFLFNBQVNsRDtvREFDVG1FLFVBQVUzRjtvREFDVnFFLFdBQVU7O3NFQUVWLDhEQUFDM0YsMktBQUNBOzREQUFDMkYsV0FBVTs7Ozs7O3dEQUF3Qjs7Ozs7Ozs4REFHdkMsOERBQUNJO29EQUNDdEUsTUFBSztvREFDTHdGLFVBQVUzRjtvREFDVnFFLFdBQVU7OERBRVRyRSw2QkFDQzs7MEVBQ0UsOERBQUM0RjtnRUFBSXZCLFdBQVU7Z0VBQW1Dd0IsTUFBSztnRUFBT0MsU0FBUTs7a0ZBQ3BFLDhEQUFDQzt3RUFBTzFCLFdBQVU7d0VBQWEyQixJQUFHO3dFQUFLQyxJQUFHO3dFQUFLQyxHQUFFO3dFQUFLQyxRQUFPO3dFQUFlQyxhQUFZOzs7Ozs7a0ZBQ3hGLDhEQUFDQzt3RUFBS2hDLFdBQVU7d0VBQWF3QixNQUFLO3dFQUFlUyxHQUFFOzs7Ozs7Ozs7Ozs7NERBQy9DOztxRkFJUjs7MEVBQ0UsOERBQUM3SCwyS0FBSUE7Z0VBQUM0RixXQUFVOzs7Ozs7NERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVl2RDlFLHdCQUNDLDhEQUFDNkU7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3VCO29DQUFJdkIsV0FBVTtvQ0FBZ0RrQyxPQUFNO29DQUE2QlYsTUFBSztvQ0FBT0MsU0FBUTs7c0RBQ3BILDhEQUFDQzs0Q0FBTzFCLFdBQVU7NENBQWEyQixJQUFHOzRDQUFLQyxJQUFHOzRDQUFLQyxHQUFFOzRDQUFLQyxRQUFPOzRDQUFlQyxhQUFZOzs7Ozs7c0RBQ3hGLDhEQUFDQzs0Q0FBS2hDLFdBQVU7NENBQWF3QixNQUFLOzRDQUFlUyxHQUFFOzs7Ozs7Ozs7Ozs7Z0NBQy9DO2dDQUNHcEg7Z0NBQVc7Ozs7Ozs7Ozs7OytCQUd0QkQsUUFBUXVGLE1BQU0sS0FBSyxrQkFDckIsOERBQUNKO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFXLG9CQUFtQyxPQUFmSCxPQUFPbkMsT0FBTzs4Q0FDaEQsNEVBQUNvQzt3Q0FBY0UsV0FBVyxXQUE0QixPQUFqQkgsT0FBT2xDLFNBQVM7Ozs7Ozs7Ozs7OzhDQUV2RCw4REFBQ29DOztzREFDQyw4REFBQ0U7NENBQUdELFdBQVU7O2dEQUF5QztnREFDakRuRjtnREFBVzs7Ozs7OztzREFFakIsOERBQUNxRjs0Q0FBRUYsV0FBVTs7Z0RBQTZCO2dEQUNyQm5GO2dEQUFXOzs7Ozs7O3NEQUVoQyw4REFBQ3VGOzRDQUNDQyxTQUFTL0Q7NENBQ1QwRCxXQUFVOzs4REFFViw4REFBQy9GLDBLQUFJQTtvREFBQytGLFdBQVU7Ozs7OztnREFBaUI7Z0RBQzVCbkY7Z0RBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzZDQU14Qiw4REFBQ2tGO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDbUM7NEJBQU1uQyxXQUFVOzs4Q0FDZiw4REFBQ29DO29DQUFNcEMsV0FBVTs4Q0FDZiw0RUFBQ3FDOzswREFDQyw4REFBQ0M7Z0RBQUdDLE9BQU07Z0RBQU12QyxXQUFVOzBEQUFpRjs7Ozs7OzBEQUczRyw4REFBQ3NDO2dEQUFHQyxPQUFNO2dEQUFNdkMsV0FBVTswREFBaUY7Ozs7OzswREFHM0csOERBQUNzQztnREFBR0MsT0FBTTtnREFBTXZDLFdBQVU7MERBQWlGOzs7Ozs7NENBR3pHbkYsQ0FBQUEsZUFBZSxRQUFRQSxlQUFlLEtBQUksbUJBQzFDLDhEQUFDeUg7Z0RBQUdDLE9BQU07Z0RBQU12QyxXQUFVOzBEQUFpRjs7Ozs7OzBEQUk3Ryw4REFBQ3NDO2dEQUFHQyxPQUFNO2dEQUFNdkMsV0FBVTswREFDeEIsNEVBQUNXO29EQUFLWCxXQUFVOzhEQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUloQyw4REFBQ3dDO29DQUFNeEMsV0FBVTs4Q0FHZHBGLFFBQVE2RixHQUFHLENBQUMsQ0FBQ2YsdUJBQ1osOERBQUMyQzs0Q0FFQ3JDLFdBQVU7OzhEQUdWLDhEQUFDeUM7b0RBQUd6QyxXQUFVOzhEQUNaLDRFQUFDRDt3REFBSUMsV0FBVTtrRUFDWmhCLGlCQUFpQlUsT0FBT3RELElBQUk7Ozs7Ozs7Ozs7OzhEQUtqQyw4REFBQ3FHO29EQUFHekMsV0FBVTs4REFDWiw0RUFBQ0Q7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDRDtnRUFBSUMsV0FBVTswRUFDWk4sT0FBT2dELE9BQU87Ozs7OzswRUFFakIsOERBQUN0QztnRUFDQ0MsU0FBUyxJQUFNbEIsZ0JBQWdCTyxPQUFPZ0QsT0FBTztnRUFDN0MxQyxXQUFVO2dFQUNWMkMsT0FBTTswRUFFTiw0RUFBQzNJLDJLQUFJQTtvRUFBQ2dHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBTXRCLDhEQUFDeUM7b0RBQUd6QyxXQUFVOzhEQUNaLDRFQUFDVzt3REFBS1gsV0FBVTs7MEVBQ2QsOERBQUM3RiwyS0FBS0E7Z0VBQUM2RixXQUFVOzs7Ozs7NERBQ2hCN0IsVUFBVXVCLE9BQU90QixHQUFHOzs7Ozs7Ozs7Ozs7Z0RBS3ZCdkQsQ0FBQUEsZUFBZSxRQUFRQSxlQUFlLEtBQUksbUJBQzFDLDhEQUFDNEg7b0RBQUd6QyxXQUFVOzhEQUNaLDRFQUFDRDt3REFBSUMsV0FBVTtrRUFDWk4sT0FBT2tELFFBQVEsSUFBSTs7Ozs7Ozs7Ozs7OERBTTFCLDhEQUFDSDtvREFBR3pDLFdBQVU7OERBQ1osNEVBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0k7Z0VBQ0NDLFNBQVMsSUFBTXZGLFVBQVVBLE9BQU80RTtnRUFDaENNLFdBQVU7Z0VBQ1YyQyxPQUFNOzBFQUVOLDRFQUFDN0ksMktBQUlBO29FQUFDa0csV0FBVTs7Ozs7Ozs7Ozs7MEVBRWxCLDhEQUFDSTtnRUFDQ0MsU0FBUyxJQUFNWixrQkFBa0JDO2dFQUNqQ00sV0FBVTtnRUFDVjJDLE9BQU07MEVBRU4sNEVBQUM1SSwyS0FBTUE7b0VBQUNpRyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQ0ExRG5CTixPQUFPRSxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUF1RTNCekUsK0JBQ0MsOERBQUM0RTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDYiw0RUFBQzlGLDJLQUFhQTs0Q0FBQzhGLFdBQVU7Ozs7Ozs7Ozs7O2tEQUUzQiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNDOzRDQUFHRCxXQUFVO3NEQUE4Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBS2hFLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUFFRixXQUFVOzs0Q0FBNkI7NENBQ0Y3RSwwQkFBQUEsb0NBQUFBLGNBQWVXLElBQUk7NENBQUM7Ozs7Ozs7a0RBRTVELDhEQUFDaUU7d0NBQUlDLFdBQVU7a0RBQ2IsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ1poQixpQkFBaUI3RCwwQkFBQUEsb0NBQUFBLGNBQWVpQixJQUFJOzs7Ozs7OERBRXZDLDhEQUFDMkQ7b0RBQUlDLFdBQVU7OERBQ1o3RSwwQkFBQUEsb0NBQUFBLGNBQWV1SCxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLL0IsOERBQUMzQztnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNJO3dDQUNDQyxTQUFTLElBQU1qRixpQkFBaUI7d0NBQ2hDNEUsV0FBVTtrREFDWDs7Ozs7O2tEQUdELDhEQUFDSTt3Q0FDQ0MsU0FBU1Y7d0NBQ1RLLFdBQVU7a0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVVqQjtHQWprQndCckY7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvZG9tYWlucy9EbnNSZWNvcmRUYWJsZS5qc3g/YjJkOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7XG4gIEdsb2JlLFxuICBMaW5rLFxuICBNYWlsLFxuICBGaWxlVGV4dCxcbiAgU2VydmVyLFxuICBTZXR0aW5ncyxcbiAgRWRpdCxcbiAgVHJhc2gyLFxuICBDb3B5LFxuICBQbHVzLFxuICBBbGVydFRyaWFuZ2xlLFxuICBDbG9jayxcbiAgU2F2ZSxcbiAgWCxcbiAgSW5mbyxcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IHtcbiAgVFRMX09QVElPTlMsXG4gIEROU19SRUNPUkRfVFlQRVMsXG4gIERFRkFVTFRfVFRMLFxuICB2YWxpZGF0ZURuc1JlY29yZFxufSBmcm9tIFwiQC9jb25zdGFudHMvZG5zUmVjb3Jkc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEbnNSZWNvcmRUYWJsZSh7XG4gIHJlY29yZHMsXG4gIHJlY29yZFR5cGUsXG4gIG9uRWRpdCxcbiAgb25EZWxldGUsXG4gIG9uQWRkLFxuICBkb21haW4sXG4gIGxvYWRpbmcgPSBmYWxzZSxcbn0pIHtcbiAgY29uc3QgW2RlbGV0ZUNvbmZpcm0sIHNldERlbGV0ZUNvbmZpcm1dID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtzaG93SW5saW5lRm9ybSwgc2V0U2hvd0lubGluZUZvcm1dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlKHt9KTtcbiAgY29uc3QgW2Zvcm1FcnJvcnMsIHNldEZvcm1FcnJvcnNdID0gdXNlU3RhdGUoe30pO1xuICBjb25zdCBbaXNTdWJtaXR0aW5nLCBzZXRJc1N1Ym1pdHRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIFJlc2V0IGZvcm0gc3RhdGUgd2hlbiByZWNvcmQgdHlwZSBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0U2hvd0lubGluZUZvcm0oZmFsc2UpO1xuICAgIHNldEZvcm1EYXRhKHt9KTtcbiAgICBzZXRGb3JtRXJyb3JzKHt9KTtcbiAgfSwgW3JlY29yZFR5cGVdKTtcblxuICAvLyBJbml0aWFsaXplIGZvcm0gZGF0YSBmb3IgdGhlIGN1cnJlbnQgcmVjb3JkIHR5cGVcbiAgY29uc3QgaW5pdGlhbGl6ZUZvcm1EYXRhID0gKHR5cGUpID0+IHtcbiAgICBjb25zdCByZWNvcmRUeXBlQ29uZmlnID0gRE5TX1JFQ09SRF9UWVBFU1t0eXBlXTtcbiAgICBpZiAoIXJlY29yZFR5cGVDb25maWcpIHJldHVybiB7IHR5cGUgfTtcblxuICAgIGNvbnN0IGluaXRpYWxEYXRhID0geyB0eXBlIH07XG4gICAgcmVjb3JkVHlwZUNvbmZpZy5maWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7XG4gICAgICBpZiAoZmllbGQubmFtZSA9PT0gJ3R0bCcpIHtcbiAgICAgICAgaW5pdGlhbERhdGFbZmllbGQubmFtZV0gPSBmaWVsZC5kZWZhdWx0IHx8IERFRkFVTFRfVFRMO1xuICAgICAgfSBlbHNlIGlmIChmaWVsZC50eXBlID09PSAnbnVtYmVyJykge1xuICAgICAgICBpbml0aWFsRGF0YVtmaWVsZC5uYW1lXSA9IGZpZWxkLmRlZmF1bHQgfHwgMDtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGluaXRpYWxEYXRhW2ZpZWxkLm5hbWVdID0gZmllbGQuZGVmYXVsdCB8fCAnJztcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIHJldHVybiBpbml0aWFsRGF0YTtcbiAgfTtcblxuICAvLyBIYW5kbGUgYWRkIGJ1dHRvbiBjbGljayAtIHNob3dzIGlubGluZSBmb3JtXG4gIGNvbnN0IGhhbmRsZUFkZENsaWNrID0gKCkgPT4ge1xuICAgIGNvbnN0IGluaXRpYWxEYXRhID0gaW5pdGlhbGl6ZUZvcm1EYXRhKHJlY29yZFR5cGUpO1xuICAgIHNldEZvcm1EYXRhKGluaXRpYWxEYXRhKTtcbiAgICBzZXRGb3JtRXJyb3JzKHt9KTtcbiAgICBzZXRTaG93SW5saW5lRm9ybSh0cnVlKTtcbiAgfTtcblxuICAvLyBIYW5kbGUgZm9ybSBmaWVsZCBjaGFuZ2VzXG4gIGNvbnN0IGhhbmRsZUZpZWxkQ2hhbmdlID0gKGZpZWxkTmFtZSwgdmFsdWUpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgW2ZpZWxkTmFtZV06IHZhbHVlXG4gICAgfSkpO1xuXG4gICAgLy8gQ2xlYXIgZXJyb3IgZm9yIHRoaXMgZmllbGRcbiAgICBpZiAoZm9ybUVycm9yc1tmaWVsZE5hbWVdKSB7XG4gICAgICBzZXRGb3JtRXJyb3JzKHByZXYgPT4gKHtcbiAgICAgICAgLi4ucHJldixcbiAgICAgICAgW2ZpZWxkTmFtZV06IG51bGxcbiAgICAgIH0pKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIGZvcm0gc3VibWlzc2lvblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZSkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRJc1N1Ym1pdHRpbmcodHJ1ZSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gVmFsaWRhdGUgdGhlIGZvcm0gZGF0YVxuICAgICAgY29uc3QgdmFsaWRhdGlvbiA9IHZhbGlkYXRlRG5zUmVjb3JkKGZvcm1EYXRhKTtcbiAgICAgIGlmICghdmFsaWRhdGlvbi5pc1ZhbGlkKSB7XG4gICAgICAgIHNldEZvcm1FcnJvcnModmFsaWRhdGlvbi5lcnJvcnMpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGF3YWl0IG9uQWRkKGZvcm1EYXRhKTtcbiAgICAgIHNldFNob3dJbmxpbmVGb3JtKGZhbHNlKTtcbiAgICAgIHNldEZvcm1EYXRhKHt9KTtcbiAgICAgIHNldEZvcm1FcnJvcnMoe30pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhZGRpbmcgcmVjb3JkOicsIGVycm9yKTtcbiAgICAgIC8vIEhhbmRsZSB2YWxpZGF0aW9uIGVycm9ycyBpZiBuZWVkZWRcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNTdWJtaXR0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gSGFuZGxlIGZvcm0gY2FuY2VsXG4gIGNvbnN0IGhhbmRsZUNhbmNlbCA9ICgpID0+IHtcbiAgICBzZXRTaG93SW5saW5lRm9ybShmYWxzZSk7XG4gICAgc2V0Rm9ybURhdGEoe30pO1xuICAgIHNldEZvcm1FcnJvcnMoe30pO1xuICB9O1xuXG5cblxuICAvLyBHZXQgcmVjb3JkIHR5cGUgY29uZmlndXJhdGlvblxuICBjb25zdCBnZXRSZWNvcmRUeXBlQ29uZmlnID0gKHR5cGUpID0+IHtcbiAgICBjb25zdCBjb25maWdzID0ge1xuICAgICAgQToge1xuICAgICAgICBpY29uOiBHbG9iZSxcbiAgICAgICAgbGFiZWw6IFwiSVB2NCBBZGRyZXNzXCIsXG4gICAgICAgIGNvbG9yOiBcImJsdWVcIixcbiAgICAgICAgYmdDb2xvcjogXCJiZy1ibHVlLTUwXCIsXG4gICAgICAgIHRleHRDb2xvcjogXCJ0ZXh0LWJsdWUtNjAwXCIsXG4gICAgICAgIGJvcmRlckNvbG9yOiBcImJvcmRlci1ibHVlLTIwMFwiLFxuICAgICAgfSxcbiAgICAgIEFBQUE6IHtcbiAgICAgICAgaWNvbjogR2xvYmUsXG4gICAgICAgIGxhYmVsOiBcIklQdjYgQWRkcmVzc1wiLFxuICAgICAgICBjb2xvcjogXCJwdXJwbGVcIixcbiAgICAgICAgYmdDb2xvcjogXCJiZy1wdXJwbGUtNTBcIixcbiAgICAgICAgdGV4dENvbG9yOiBcInRleHQtcHVycGxlLTYwMFwiLFxuICAgICAgICBib3JkZXJDb2xvcjogXCJib3JkZXItcHVycGxlLTIwMFwiLFxuICAgICAgfSxcbiAgICAgIENOQU1FOiB7XG4gICAgICAgIGljb246IExpbmssXG4gICAgICAgIGxhYmVsOiBcIkNhbm9uaWNhbCBOYW1lXCIsXG4gICAgICAgIGNvbG9yOiBcImdyZWVuXCIsXG4gICAgICAgIGJnQ29sb3I6IFwiYmctZ3JlZW4tNTBcIixcbiAgICAgICAgdGV4dENvbG9yOiBcInRleHQtZ3JlZW4tNjAwXCIsXG4gICAgICAgIGJvcmRlckNvbG9yOiBcImJvcmRlci1ncmVlbi0yMDBcIixcbiAgICAgIH0sXG4gICAgICBNWDoge1xuICAgICAgICBpY29uOiBNYWlsLFxuICAgICAgICBsYWJlbDogXCJNYWlsIEV4Y2hhbmdlXCIsXG4gICAgICAgIGNvbG9yOiBcIm9yYW5nZVwiLFxuICAgICAgICBiZ0NvbG9yOiBcImJnLW9yYW5nZS01MFwiLFxuICAgICAgICB0ZXh0Q29sb3I6IFwidGV4dC1vcmFuZ2UtNjAwXCIsXG4gICAgICAgIGJvcmRlckNvbG9yOiBcImJvcmRlci1vcmFuZ2UtMjAwXCIsXG4gICAgICB9LFxuICAgICAgVFhUOiB7XG4gICAgICAgIGljb246IEZpbGVUZXh0LFxuICAgICAgICBsYWJlbDogXCJUZXh0IFJlY29yZFwiLFxuICAgICAgICBjb2xvcjogXCJncmF5XCIsXG4gICAgICAgIGJnQ29sb3I6IFwiYmctZ3JheS01MFwiLFxuICAgICAgICB0ZXh0Q29sb3I6IFwidGV4dC1ncmF5LTYwMFwiLFxuICAgICAgICBib3JkZXJDb2xvcjogXCJib3JkZXItZ3JheS0yMDBcIixcbiAgICAgIH0sXG4gICAgICBOUzoge1xuICAgICAgICBpY29uOiBTZXJ2ZXIsXG4gICAgICAgIGxhYmVsOiBcIk5hbWUgU2VydmVyXCIsXG4gICAgICAgIGNvbG9yOiBcImluZGlnb1wiLFxuICAgICAgICBiZ0NvbG9yOiBcImJnLWluZGlnby01MFwiLFxuICAgICAgICB0ZXh0Q29sb3I6IFwidGV4dC1pbmRpZ28tNjAwXCIsXG4gICAgICAgIGJvcmRlckNvbG9yOiBcImJvcmRlci1pbmRpZ28tMjAwXCIsXG4gICAgICB9LFxuICAgICAgU1JWOiB7XG4gICAgICAgIGljb246IFNldHRpbmdzLFxuICAgICAgICBsYWJlbDogXCJTZXJ2aWNlIFJlY29yZFwiLFxuICAgICAgICBjb2xvcjogXCJwaW5rXCIsXG4gICAgICAgIGJnQ29sb3I6IFwiYmctcGluay01MFwiLFxuICAgICAgICB0ZXh0Q29sb3I6IFwidGV4dC1waW5rLTYwMFwiLFxuICAgICAgICBib3JkZXJDb2xvcjogXCJib3JkZXItcGluay0yMDBcIixcbiAgICAgIH0sXG4gICAgfTtcbiAgICByZXR1cm4gY29uZmlnc1t0eXBlXSB8fCBjb25maWdzLkE7XG4gIH07XG5cbiAgLy8gRm9ybWF0IFRUTCBkaXNwbGF5XG4gIGNvbnN0IGZvcm1hdFRUTCA9ICh0dGwpID0+IHtcbiAgICBjb25zdCBudW1lcmljVFRMID0gcGFyc2VJbnQodHRsKTtcbiAgICBjb25zdCBvcHRpb24gPSBUVExfT1BUSU9OUy5maW5kKChvcHQpID0+IG9wdC52YWx1ZSA9PT0gbnVtZXJpY1RUTCk7XG4gICAgaWYgKG9wdGlvbikge1xuICAgICAgcmV0dXJuIG9wdGlvbi5sYWJlbDtcbiAgICB9XG4gICAgXG4gICAgLy8gRm9ybWF0IGN1c3RvbSBUVEwgdmFsdWVzXG4gICAgaWYgKG51bWVyaWNUVEwgPj0gODY0MDApIHtcbiAgICAgIGNvbnN0IGRheXMgPSBNYXRoLmZsb29yKG51bWVyaWNUVEwgLyA4NjQwMCk7XG4gICAgICBjb25zdCByZW1haW5kZXIgPSBudW1lcmljVFRMICUgODY0MDA7XG4gICAgICBpZiAocmVtYWluZGVyID09PSAwKSB7XG4gICAgICAgIHJldHVybiBgJHtkYXlzfSBkYXkke2RheXMgIT09IDEgPyAncycgOiAnJ31gO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAobnVtZXJpY1RUTCA+PSAzNjAwKSB7XG4gICAgICBjb25zdCBob3VycyA9IE1hdGguZmxvb3IobnVtZXJpY1RUTCAvIDM2MDApO1xuICAgICAgY29uc3QgcmVtYWluZGVyID0gbnVtZXJpY1RUTCAlIDM2MDA7XG4gICAgICBpZiAocmVtYWluZGVyID09PSAwKSB7XG4gICAgICAgIHJldHVybiBgJHtob3Vyc30gaG91ciR7aG91cnMgIT09IDEgPyAncycgOiAnJ31gO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAobnVtZXJpY1RUTCA+PSA2MCkge1xuICAgICAgY29uc3QgbWludXRlcyA9IE1hdGguZmxvb3IobnVtZXJpY1RUTCAvIDYwKTtcbiAgICAgIGNvbnN0IHJlbWFpbmRlciA9IG51bWVyaWNUVEwgJSA2MDtcbiAgICAgIGlmIChyZW1haW5kZXIgPT09IDApIHtcbiAgICAgICAgcmV0dXJuIGAke21pbnV0ZXN9IG1pbnV0ZSR7bWludXRlcyAhPT0gMSA/ICdzJyA6ICcnfWA7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBgJHtudW1lcmljVFRMfXNgO1xuICB9O1xuXG4gIC8vIEZvcm1hdCByZWNvcmQgbmFtZVxuICBjb25zdCBmb3JtYXRSZWNvcmROYW1lID0gKG5hbWUpID0+IHtcbiAgICBpZiAoIW5hbWUgfHwgbmFtZSA9PT0gXCJAXCIpIHJldHVybiBkb21haW4/Lm5hbWUgfHwgXCJAXCI7XG4gICAgaWYgKG5hbWUuZW5kc1dpdGgoXCIuXCIpKSByZXR1cm4gbmFtZS5zbGljZSgwLCAtMSk7XG4gICAgcmV0dXJuIG5hbWU7XG4gIH07XG5cbiAgLy8gQ29weSB0byBjbGlwYm9hcmRcbiAgY29uc3QgY29weVRvQ2xpcGJvYXJkID0gYXN5bmMgKHRleHQpID0+IHtcbiAgICB0cnkge1xuICAgICAgYXdhaXQgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQodGV4dCk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRmFpbGVkIHRvIGNvcHk6XCIsIGVycik7XG4gICAgfVxuICB9O1xuXG4gIC8vIEhhbmRsZSBkZWxldGUgY29uZmlybWF0aW9uXG4gIGNvbnN0IGhhbmRsZURlbGV0ZUNsaWNrID0gKHJlY29yZCkgPT4ge1xuICAgIHNldERlbGV0ZUNvbmZpcm0ocmVjb3JkKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEZWxldGVDb25maXJtID0gKCkgPT4ge1xuICAgIGlmIChkZWxldGVDb25maXJtICYmIG9uRGVsZXRlKSB7XG4gICAgICBvbkRlbGV0ZShkZWxldGVDb25maXJtLmlkLCBkZWxldGVDb25maXJtKTtcbiAgICB9XG4gICAgc2V0RGVsZXRlQ29uZmlybShudWxsKTtcbiAgfTtcblxuICBjb25zdCBjb25maWcgPSBnZXRSZWNvcmRUeXBlQ29uZmlnKHJlY29yZFR5cGUpO1xuICBjb25zdCBJY29uQ29tcG9uZW50ID0gY29uZmlnLmljb247XG5cbiAgcmV0dXJuIChcbiAgICA8PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcC0yIHJvdW5kZWQtbGcgJHtjb25maWcuYmdDb2xvcn0gYm9yZGVyIGJvcmRlci1ncmF5LTIwMGB9PlxuICAgICAgICAgICAgICA8SWNvbkNvbXBvbmVudCBjbGFzc05hbWU9e2BoLTUgdy01ICR7Y29uZmlnLnRleHRDb2xvcn1gfSAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAge3JlY29yZFR5cGV9IFJlY29yZHNcbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAge2NvbmZpZy5sYWJlbH0g4oCiIHtyZWNvcmRzLmxlbmd0aH0gcmVjb3Jke3JlY29yZHMubGVuZ3RoICE9PSAxID8gJ3MnIDogJyd9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIHshc2hvd0lubGluZUZvcm0gJiYgKFxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVBZGRDbGlja31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgc2hhZG93LXNtIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBiZy13aGl0ZSBob3ZlcjpiZy1ncmF5LTUwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgQWRkIHtyZWNvcmRUeXBlfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIElubGluZSBBZGQgRm9ybSAqL31cbiAgICAgICAge3Nob3dJbmxpbmVGb3JtICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgYmctYmx1ZS01MCBib3JkZXIgYm9yZGVyLWJsdWUtMjAwIHJvdW5kZWQtbGcgcC02XCI+XG4gICAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICBBZGQge3JlY29yZFR5cGV9IFJlY29yZFxuICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICB7cmVjb3JkVHlwZSA9PT0gJ1NSVidcbiAgICAgICAgICAgICAgICAgICAgPyAnQ29uZmlndXJlIHNlcnZpY2UgbG9jYXRpb24gd2l0aCBwcmlvcml0eSwgd2VpZ2h0LCBwb3J0LCBhbmQgdGFyZ2V0IGhvc3QnXG4gICAgICAgICAgICAgICAgICAgIDogYEFkZCBhIG5ldyAke3JlY29yZFR5cGV9IHJlY29yZCBmb3IgJHtkb21haW4/Lm5hbWV9YFxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIEZvcm0gRmllbGRzICovfVxuICAgICAgICAgICAgICB7KCgpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCByZWNvcmRUeXBlQ29uZmlnID0gRE5TX1JFQ09SRF9UWVBFU1tyZWNvcmRUeXBlXTtcbiAgICAgICAgICAgICAgICBpZiAoIXJlY29yZFR5cGVDb25maWcpIHJldHVybiBudWxsO1xuXG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGRzID0gcmVjb3JkVHlwZUNvbmZpZy5maWVsZHM7XG5cbiAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAge2ZpZWxkcy5tYXAoKGZpZWxkKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2ZpZWxkLm5hbWV9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmaWVsZC5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2ZpZWxkLnJlcXVpcmVkICYmIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCBtbC0xXCI+Kjwvc3Bhbj59XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAge2ZpZWxkLnR5cGUgPT09ICdzZWxlY3QnICYmIGZpZWxkLm5hbWUgPT09ICd0dGwnID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YVtmaWVsZC5uYW1lXSB8fCBmaWVsZC5kZWZhdWx0IHx8IERFRkFVTFRfVFRMfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGaWVsZENoYW5nZShmaWVsZC5uYW1lLCBwYXJzZUludChlLnRhcmdldC52YWx1ZSkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1RUTF9PUFRJT05TLm1hcCgob3B0aW9uKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24ga2V5PXtvcHRpb24udmFsdWV9IHZhbHVlPXtvcHRpb24udmFsdWV9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtvcHRpb24ubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3JlY29yZFR5cGUgPT09ICdTUlYnICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTEgdGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIERlZmF1bHQ6IHtmb3JtYXRUVEwoZm9ybURhdGFbZmllbGQubmFtZV0gfHwgZmllbGQuZGVmYXVsdCB8fCBERUZBVUxUX1RUTCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICApIDogZmllbGQudHlwZSA9PT0gJ251bWJlcicgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YVtmaWVsZC5uYW1lXSB8fCAocmVjb3JkVHlwZSA9PT0gJ1NSVicgJiYgWydwcmlvcml0eScsICd3ZWlnaHQnLCAncG9ydCddLmluY2x1ZGVzKGZpZWxkLm5hbWUpID8gMCA6ICcnKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpZWxkQ2hhbmdlKGZpZWxkLm5hbWUsIHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB8fCAwKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17cmVjb3JkVHlwZSA9PT0gJ1NSVicgJiYgZmllbGQubmFtZSA9PT0gJ3BvcnQnID8gJzgwLCA0NDMsIDUwNjAsIGV0Yy4nIDogZmllbGQucGxhY2Vob2xkZXJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWluPXtmaWVsZC5taW59XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWF4PXtmaWVsZC5tYXh9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApIDogZmllbGQudHlwZSA9PT0gJ3RleHRhcmVhJyA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhW2ZpZWxkLm5hbWVdIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlRmllbGRDaGFuZ2UoZmllbGQubmFtZSwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtmaWVsZC5wbGFjZWhvbGRlcn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3dzPXsyfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YVtmaWVsZC5uYW1lXSB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpZWxkQ2hhbmdlKGZpZWxkLm5hbWUsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZWNvcmRUeXBlID09PSAnU1JWJyAmJiBmaWVsZC5uYW1lID09PSAnbmFtZSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgX3NlcnZpY2UuX3Byb3RvY29sIChlLmcuLCBfc2lwLl90Y3ApYFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHJlY29yZFR5cGUgPT09ICdTUlYnICYmIGZpZWxkLm5hbWUgPT09ICdjb250ZW50J1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGB0YXJnZXQuJHtkb21haW4/Lm5hbWUgfHwgJ2V4YW1wbGUuY29tJ31gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogZmllbGQucGxhY2Vob2xkZXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1FcnJvcnNbZmllbGQubmFtZV0gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0xIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Zvcm1FcnJvcnNbZmllbGQubmFtZV19PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICB9KSgpfVxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB0LTQgYm9yZGVyLXQgYm9yZGVyLWJsdWUtMjAwXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS03MDBcIj5cbiAgICAgICAgICAgICAgICAgIHtyZWNvcmRUeXBlID09PSAnU1JWJyA/IChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgU2VydmljZSBuYW1lIGZvcm1hdDogX3NlcnZpY2UuX3Byb3RvY29sLntkb21haW4/Lm5hbWV9IOKAoiBUYXJnZXQ6IGZ1bGx5IHF1YWxpZmllZCBkb21haW4gbmFtZVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICA8c3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICBVc2UgXCJAXCIgZm9yIHJvb3QgZG9tYWluICh7ZG9tYWluPy5uYW1lfSksIFwid3d3XCIgZm9yIHd3dy57ZG9tYWluPy5uYW1lfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtM1wiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ2FuY2VsfVxuICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNTdWJtaXR0aW5nfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTUwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWJsdWUtNTAwIGRpc2FibGVkOm9wYWNpdHktNTBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTIgaW5saW5lXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgQ2FuY2VsXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1N1Ym1pdHRpbmd9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgYmctYmx1ZS02MDAgYm9yZGVyIGJvcmRlci10cmFuc3BhcmVudCByb3VuZGVkLW1kIGhvdmVyOmJnLWJsdWUtNzAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWJsdWUtNTAwIGRpc2FibGVkOm9wYWNpdHktNTBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7aXNTdWJtaXR0aW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiBoLTQgdy00IG1yLTIgaW5saW5lXCIgZmlsbD1cIm5vbmVcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxjaXJjbGUgY2xhc3NOYW1lPVwib3BhY2l0eS0yNVwiIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjEwXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCI0XCI+PC9jaXJjbGU+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGNsYXNzTmFtZT1cIm9wYWNpdHktNzVcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgZD1cIk00IDEyYTggOCAwIDAxOC04VjBDNS4zNzMgMCAwIDUuMzczIDAgMTJoNHptMiA1LjI5MUE3Ljk2MiA3Ljk2MiAwIDAxNCAxMkgwYzAgMy4wNDIgMS4xMzUgNS44MjQgMyA3LjkzOGwzLTIuNjQ3elwiPjwvcGF0aD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgICAgICAgQWRkaW5nLi4uXG4gICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxTYXZlIGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMiBpbmxpbmVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgQWRkIFJlY29yZFxuICAgICAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9mb3JtPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBUYWJsZSBDb250ZW50ICovfVxuICAgICAgICB7bG9hZGluZyA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTEyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC00IHB5LTIgZm9udC1zZW1pYm9sZCBsZWFkaW5nLTYgdGV4dC1zbSBzaGFkb3cgcm91bmRlZC1tZCB0ZXh0LWdyYXktNTAwIGJnLXdoaXRlXCI+XG4gICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIC1tbC0xIG1yLTMgaC01IHctNSB0ZXh0LWdyYXktNTAwXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgIDxjaXJjbGUgY2xhc3NOYW1lPVwib3BhY2l0eS0yNVwiIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjEwXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCI0XCI+PC9jaXJjbGU+XG4gICAgICAgICAgICAgICAgPHBhdGggY2xhc3NOYW1lPVwib3BhY2l0eS03NVwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiBkPVwiTTQgMTJhOCA4IDAgMDE4LThWMEM1LjM3MyAwIDAgNS4zNzMgMCAxMmg0em0yIDUuMjkxQTcuOTYyIDcuOTYyIDAgMDE0IDEySDBjMCAzLjA0MiAxLjEzNSA1LjgyNCAzIDcuOTM4bDMtMi42NDd6XCI+PC9wYXRoPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgTG9hZGluZyB7cmVjb3JkVHlwZX0gcmVjb3Jkcy4uLlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiByZWNvcmRzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTE2XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcC00IHJvdW5kZWQtZnVsbCAke2NvbmZpZy5iZ0NvbG9yfWB9PlxuICAgICAgICAgICAgICAgIDxJY29uQ29tcG9uZW50IGNsYXNzTmFtZT17YGgtOCB3LTggJHtjb25maWcudGV4dENvbG9yfWB9IC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAgTm8ge3JlY29yZFR5cGV9IHJlY29yZHMgZm91bmRcbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC1zbSBtYi00XCI+XG4gICAgICAgICAgICAgICAgICBDcmVhdGUgeW91ciBmaXJzdCB7cmVjb3JkVHlwZX0gcmVjb3JkIHRvIGdldCBzdGFydGVkXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUFkZENsaWNrfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBib3JkZXIgYm9yZGVyLXRyYW5zcGFyZW50IHJvdW5kZWQtbWQgc2hhZG93LXNtIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBiZy1ibHVlLTYwMCBob3ZlcjpiZy1ibHVlLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgIEFkZCB7cmVjb3JkVHlwZX0gUmVjb3JkXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJvdmVyZmxvdy1oaWRkZW4gc2hhZG93IHJpbmctMSByaW5nLWJsYWNrIHJpbmctb3BhY2l0eS01IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJtaW4tdy1mdWxsIGRpdmlkZS15IGRpdmlkZS1ncmF5LTMwMFwiPlxuICAgICAgICAgICAgICA8dGhlYWQgY2xhc3NOYW1lPVwiYmctZ3JheS01MFwiPlxuICAgICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICAgIDx0aCBzY29wZT1cImNvbFwiIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICBOYW1lXG4gICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgPHRoIHNjb3BlPVwiY29sXCIgY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIENvbnRlbnRcbiAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICA8dGggc2NvcGU9XCJjb2xcIiBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgVFRMXG4gICAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgICAgeyhyZWNvcmRUeXBlID09PSAnTVgnIHx8IHJlY29yZFR5cGUgPT09ICdTUlYnKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDx0aCBzY29wZT1cImNvbFwiIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgIFByaW9yaXR5XG4gICAgICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPHRoIHNjb3BlPVwiY29sXCIgY2xhc3NOYW1lPVwicmVsYXRpdmUgcHgtNiBweS0zXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHlcIj5BY3Rpb25zPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICA8dGJvZHkgY2xhc3NOYW1lPVwiYmctd2hpdGUgZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG5cblxuICAgICAgICAgICAgICAgIHtyZWNvcmRzLm1hcCgocmVjb3JkKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8dHJcbiAgICAgICAgICAgICAgICAgICAga2V5PXtyZWNvcmQuaWR9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImhvdmVyOmJnLWdyYXktNTAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMTUwXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgey8qIE5hbWUgQ29sdW1uICovfVxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRSZWNvcmROYW1lKHJlY29yZC5uYW1lKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cblxuICAgICAgICAgICAgICAgICAgICB7LyogQ29udGVudCBDb2x1bW4gKi99XG4gICAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tb25vIHRleHQtZ3JheS03MDAgZmxleC0xIG1pbi13LTAgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3JlY29yZC5jb250ZW50fVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGNvcHlUb0NsaXBib2FyZChyZWNvcmQuY29udGVudCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMSB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtZ3JheS02MDAgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiQ29weSB0byBjbGlwYm9hcmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q29weSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBUVEwgQ29sdW1uICovfVxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtMyB3LTMgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0VFRMKHJlY29yZC50dGwpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cblxuICAgICAgICAgICAgICAgICAgICB7LyogUHJpb3JpdHkgQ29sdW1uIChmb3IgTVggYW5kIFNSViByZWNvcmRzKSAqL31cbiAgICAgICAgICAgICAgICAgICAgeyhyZWNvcmRUeXBlID09PSAnTVgnIHx8IHJlY29yZFR5cGUgPT09ICdTUlYnKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3JlY29yZC5wcmlvcml0eSB8fCAnLSd9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgIHsvKiBBY3Rpb25zIENvbHVtbiAqL31cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LXJpZ2h0IHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb25FZGl0ICYmIG9uRWRpdChyZWNvcmQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LWJsdWUtNjAwIHJvdW5kZWQtbWQgaG92ZXI6YmctYmx1ZS01MFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRWRpdCByZWNvcmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8RWRpdCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEZWxldGVDbGljayhyZWNvcmQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgdGV4dC1ncmF5LTQwMCBob3Zlcjp0ZXh0LXJlZC02MDAgcm91bmRlZC1tZCBob3ZlcjpiZy1yZWQtNTBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkRlbGV0ZSByZWNvcmRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L3Rib2R5PlxuICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRGVsZXRlIENvbmZpcm1hdGlvbiBEaWFsb2cgKi99XG4gICAgICB7ZGVsZXRlQ29uZmlybSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ncmF5LTYwMCBiZy1vcGFjaXR5LTUwIG92ZXJmbG93LXktYXV0byBoLWZ1bGwgdy1mdWxsIHotNTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHRvcC0yMCBteC1hdXRvIHAtNSBib3JkZXIgdy05NiBzaGFkb3ctbGcgcm91bmRlZC1tZCBiZy13aGl0ZVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgbWItNFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXgtYXV0byBmbGV4LXNocmluay0wIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtMTIgdy0xMiByb3VuZGVkLWZ1bGwgYmctcmVkLTEwMFwiPlxuICAgICAgICAgICAgICAgICAgPEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXJlZC02MDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgc206dGV4dC1sZWZ0XCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBsZWFkaW5nLTYgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICBEZWxldGUgRE5TIFJlY29yZFxuICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMlwiPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICBBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIHRoaXMge2RlbGV0ZUNvbmZpcm0/LnR5cGV9IHJlY29yZD8gVGhpcyBhY3Rpb24gY2Fubm90IGJlIHVuZG9uZS5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYmctZ3JheS01MCByb3VuZGVkLWxnIGJvcmRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFJlY29yZE5hbWUoZGVsZXRlQ29uZmlybT8ubmFtZSl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbW9ubyB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2RlbGV0ZUNvbmZpcm0/LmNvbnRlbnR9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTMgbXQtNlwiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldERlbGV0ZUNvbmZpcm0obnVsbCl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgcHgtNCBweS0yIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBzaGFkb3ctc20gdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktNTAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZURlbGV0ZUNvbmZpcm19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgcHgtNCBweS0yIGJnLXJlZC02MDAgYm9yZGVyIGJvcmRlci10cmFuc3BhcmVudCByb3VuZGVkLW1kIHNoYWRvdy1zbSB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgaG92ZXI6YmctcmVkLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctb2Zmc2V0LTIgZm9jdXM6cmluZy1yZWQtNTAwXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBEZWxldGUgUmVjb3JkXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkdsb2JlIiwiTGluayIsIk1haWwiLCJGaWxlVGV4dCIsIlNlcnZlciIsIlNldHRpbmdzIiwiRWRpdCIsIlRyYXNoMiIsIkNvcHkiLCJQbHVzIiwiQWxlcnRUcmlhbmdsZSIsIkNsb2NrIiwiU2F2ZSIsIlgiLCJJbmZvIiwiVFRMX09QVElPTlMiLCJETlNfUkVDT1JEX1RZUEVTIiwiREVGQVVMVF9UVEwiLCJ2YWxpZGF0ZURuc1JlY29yZCIsIkRuc1JlY29yZFRhYmxlIiwicmVjb3JkcyIsInJlY29yZFR5cGUiLCJvbkVkaXQiLCJvbkRlbGV0ZSIsIm9uQWRkIiwiZG9tYWluIiwibG9hZGluZyIsImRlbGV0ZUNvbmZpcm0iLCJzZXREZWxldGVDb25maXJtIiwic2hvd0lubGluZUZvcm0iLCJzZXRTaG93SW5saW5lRm9ybSIsImZvcm1EYXRhIiwic2V0Rm9ybURhdGEiLCJmb3JtRXJyb3JzIiwic2V0Rm9ybUVycm9ycyIsImlzU3VibWl0dGluZyIsInNldElzU3VibWl0dGluZyIsImluaXRpYWxpemVGb3JtRGF0YSIsInR5cGUiLCJyZWNvcmRUeXBlQ29uZmlnIiwiaW5pdGlhbERhdGEiLCJmaWVsZHMiLCJmb3JFYWNoIiwiZmllbGQiLCJuYW1lIiwiZGVmYXVsdCIsImhhbmRsZUFkZENsaWNrIiwiaGFuZGxlRmllbGRDaGFuZ2UiLCJmaWVsZE5hbWUiLCJ2YWx1ZSIsInByZXYiLCJoYW5kbGVTdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJ2YWxpZGF0aW9uIiwiaXNWYWxpZCIsImVycm9ycyIsImVycm9yIiwiY29uc29sZSIsImhhbmRsZUNhbmNlbCIsImdldFJlY29yZFR5cGVDb25maWciLCJjb25maWdzIiwiQSIsImljb24iLCJsYWJlbCIsImNvbG9yIiwiYmdDb2xvciIsInRleHRDb2xvciIsImJvcmRlckNvbG9yIiwiQUFBQSIsIkNOQU1FIiwiTVgiLCJUWFQiLCJOUyIsIlNSViIsImZvcm1hdFRUTCIsInR0bCIsIm51bWVyaWNUVEwiLCJwYXJzZUludCIsIm9wdGlvbiIsImZpbmQiLCJvcHQiLCJkYXlzIiwiTWF0aCIsImZsb29yIiwicmVtYWluZGVyIiwiaG91cnMiLCJtaW51dGVzIiwiZm9ybWF0UmVjb3JkTmFtZSIsImVuZHNXaXRoIiwic2xpY2UiLCJjb3B5VG9DbGlwYm9hcmQiLCJ0ZXh0IiwibmF2aWdhdG9yIiwiY2xpcGJvYXJkIiwid3JpdGVUZXh0IiwiZXJyIiwiaGFuZGxlRGVsZXRlQ2xpY2siLCJyZWNvcmQiLCJoYW5kbGVEZWxldGVDb25maXJtIiwiaWQiLCJjb25maWciLCJJY29uQ29tcG9uZW50IiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJwIiwibGVuZ3RoIiwiYnV0dG9uIiwib25DbGljayIsImZvcm0iLCJvblN1Ym1pdCIsImg0IiwibWFwIiwicmVxdWlyZWQiLCJzcGFuIiwic2VsZWN0Iiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJpbnB1dCIsImluY2x1ZGVzIiwicGxhY2Vob2xkZXIiLCJtaW4iLCJtYXgiLCJ0ZXh0YXJlYSIsInJvd3MiLCJkaXNhYmxlZCIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwiY2lyY2xlIiwiY3giLCJjeSIsInIiLCJzdHJva2UiLCJzdHJva2VXaWR0aCIsInBhdGgiLCJkIiwieG1sbnMiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInNjb3BlIiwidGJvZHkiLCJ0ZCIsImNvbnRlbnQiLCJ0aXRsZSIsInByaW9yaXR5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/constants/dnsRecords.js":
/*!*************************************!*\
  !*** ./src/constants/dnsRecords.js ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_TTL: function() { return /* binding */ DEFAULT_TTL; },\n/* harmony export */   DNS_PRESETS: function() { return /* binding */ DNS_PRESETS; },\n/* harmony export */   DNS_RECORD_TYPES: function() { return /* binding */ DNS_RECORD_TYPES; },\n/* harmony export */   TTL_OPTIONS: function() { return /* binding */ TTL_OPTIONS; },\n/* harmony export */   checkRecordRestrictions: function() { return /* binding */ checkRecordRestrictions; },\n/* harmony export */   getDnsRecordTypes: function() { return /* binding */ getDnsRecordTypes; },\n/* harmony export */   validateDnsRecord: function() { return /* binding */ validateDnsRecord; }\n/* harmony export */ });\n// DNS Record Types and Configuration\nconst DNS_RECORD_TYPES = {\n    A: {\n        name: \"A\",\n        label: \"A Record\",\n        description: \"Points a domain to an IPv4 address\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, www, mail, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"IPv4 Address\",\n                type: \"text\",\n                required: true,\n                placeholder: \"***********\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/\n        }\n    },\n    AAAA: {\n        name: \"AAAA\",\n        label: \"AAAA Record\",\n        description: \"Points a domain to an IPv6 address\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, www, mail, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"IPv6 Address\",\n                type: \"text\",\n                required: true,\n                placeholder: \"2001:0db8:85a3:0000:0000:8a2e:0370:7334\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/\n        }\n    },\n    CNAME: {\n        name: \"CNAME\",\n        label: \"CNAME Record\",\n        description: \"Points a domain to another domain name\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"www, blog, shop, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"Target Domain\",\n                type: \"text\",\n                required: true,\n                placeholder: \"example.com\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/\n        },\n        restrictions: {\n            nameCannotBe: [\n                \"@\"\n            ],\n            message: \"CNAME records cannot be used for the root domain (@). Use A or AAAA records instead.\"\n        }\n    },\n    MX: {\n        name: \"MX\",\n        label: \"MX Record\",\n        description: \"Specifies mail servers for the domain\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, mail, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"Mail Server\",\n                type: \"text\",\n                required: true,\n                placeholder: \"mail.example.com\"\n            },\n            {\n                name: \"priority\",\n                label: \"Priority\",\n                type: \"number\",\n                required: true,\n                placeholder: \"10\",\n                min: 0,\n                max: 65535\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/,\n            priority: /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/\n        }\n    },\n    TXT: {\n        name: \"TXT\",\n        label: \"TXT Record\",\n        description: \"Stores text information for various purposes (SPF, DKIM, verification, etc.)\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"@, _dmarc, _spf, etc.\"\n            },\n            {\n                name: \"content\",\n                label: \"Text Content\",\n                type: \"textarea\",\n                required: true,\n                placeholder: \"v=spf1 include:_spf.google.com ~all\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^.{1,255}$/\n        }\n    },\n    NS: {\n        name: \"NS\",\n        label: \"NS Record\",\n        description: \"Delegates a subdomain to different nameservers\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Subdomain\",\n                type: \"text\",\n                required: true,\n                placeholder: \"subdomain\"\n            },\n            {\n                name: \"content\",\n                label: \"Nameserver\",\n                type: \"text\",\n                required: true,\n                placeholder: \"ns1.example.com\"\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/\n        },\n        restrictions: {\n            nameCannotBe: [\n                \"@\"\n            ],\n            message: \"NS records for the root domain (@) should be managed through nameserver settings, not DNS records.\"\n        }\n    },\n    SRV: {\n        name: \"SRV\",\n        label: \"SRV Record\",\n        description: \"Specifies the location of services (port and hostname)\",\n        fields: [\n            {\n                name: \"name\",\n                label: \"Service Record Name\",\n                type: \"text\",\n                required: true,\n                placeholder: \"_service._protocol (e.g., _sip._tcp)\"\n            },\n            {\n                name: \"content\",\n                label: \"Target\",\n                type: \"text\",\n                required: true,\n                placeholder: \"target.example.com\"\n            },\n            {\n                name: \"priority\",\n                label: \"Priority\",\n                type: \"number\",\n                required: true,\n                placeholder: \"0\",\n                default: 0,\n                min: 0,\n                max: 65535\n            },\n            {\n                name: \"weight\",\n                label: \"Weight\",\n                type: \"number\",\n                required: true,\n                placeholder: \"0\",\n                default: 0,\n                min: 0,\n                max: 65535\n            },\n            {\n                name: \"port\",\n                label: \"Port\",\n                type: \"number\",\n                required: true,\n                placeholder: \"0\",\n                default: 0,\n                min: 0,\n                max: 65535\n            },\n            {\n                name: \"ttl\",\n                label: \"TTL\",\n                type: \"select\",\n                required: true,\n                default: 14400\n            }\n        ],\n        validation: {\n            name: /^_[a-zA-Z0-9-]+\\._[a-zA-Z0-9-]+$/,\n            content: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\\.?$/,\n            priority: /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/,\n            weight: /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/,\n            port: /^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|[1-9][0-9][0-9][0-9]|[1-5][0-9][0-9][0-9][0-9]|6[0-4][0-9][0-9][0-9]|65[0-4][0-9][0-9]|655[0-2][0-9]|6553[0-5])$/\n        }\n    }\n};\n// TTL (Time To Live) options in seconds\nconst TTL_OPTIONS = [\n    {\n        value: 300,\n        label: \"5 minutes\"\n    },\n    {\n        value: 600,\n        label: \"10 minutes\"\n    },\n    {\n        value: 1800,\n        label: \"30 minutes\"\n    },\n    {\n        value: 3600,\n        label: \"1 hour\"\n    },\n    {\n        value: 7200,\n        label: \"2 hours\"\n    },\n    {\n        value: 14400,\n        label: \"4 hours\"\n    },\n    {\n        value: 28800,\n        label: \"8 hours\"\n    },\n    {\n        value: 43200,\n        label: \"12 hours\"\n    },\n    {\n        value: 86400,\n        label: \"1 day\"\n    },\n    {\n        value: 172800,\n        label: \"2 days\"\n    },\n    {\n        value: 604800,\n        label: \"1 week\"\n    }\n];\n// Default TTL value\nconst DEFAULT_TTL = 14400;\n// Common DNS record presets for quick setup\nconst DNS_PRESETS = {\n    \"Google Workspace\": {\n        description: \"Configure Google Workspace email and services\",\n        records: [\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"aspmx.l.google.com\",\n                priority: 1,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt1.aspmx.l.google.com\",\n                priority: 5,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt2.aspmx.l.google.com\",\n                priority: 5,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt3.aspmx.l.google.com\",\n                priority: 10,\n                ttl: 14400\n            },\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"alt4.aspmx.l.google.com\",\n                priority: 10,\n                ttl: 14400\n            },\n            {\n                type: \"TXT\",\n                name: \"@\",\n                content: \"v=spf1 include:_spf.google.com ~all\",\n                ttl: 14400\n            }\n        ]\n    },\n    \"Microsoft 365\": {\n        description: \"Configure Microsoft 365 email and services\",\n        records: [\n            {\n                type: \"MX\",\n                name: \"@\",\n                content: \"{domain}.mail.protection.outlook.com\",\n                priority: 0,\n                ttl: 14400\n            },\n            {\n                type: \"TXT\",\n                name: \"@\",\n                content: \"v=spf1 include:spf.protection.outlook.com -all\",\n                ttl: 14400\n            },\n            {\n                type: \"CNAME\",\n                name: \"autodiscover\",\n                content: \"autodiscover.outlook.com\",\n                ttl: 14400\n            }\n        ]\n    },\n    Cloudflare: {\n        description: \"Basic Cloudflare setup\",\n        records: [\n            {\n                type: \"A\",\n                name: \"@\",\n                content: \"*********\",\n                ttl: 300\n            },\n            {\n                type: \"A\",\n                name: \"www\",\n                content: \"*********\",\n                ttl: 300\n            }\n        ]\n    }\n};\n// Validation helper functions\nconst validateDnsRecord = (type, field, value)=>{\n    const recordType = DNS_RECORD_TYPES[type];\n    if (!recordType || !recordType.validation || !recordType.validation[field]) {\n        return {\n            isValid: true\n        };\n    }\n    const regex = recordType.validation[field];\n    const isValid = regex.test(value);\n    return {\n        isValid,\n        message: isValid ? null : getValidationMessage(type, field)\n    };\n};\nconst getValidationMessage = (type, field)=>{\n    var _messages_type;\n    const messages = {\n        A: {\n            content: \"Please enter a valid IPv4 address (e.g., ***********)\"\n        },\n        AAAA: {\n            content: \"Please enter a valid IPv6 address (e.g., 2001:0db8:85a3::8a2e:0370:7334)\"\n        },\n        CNAME: {\n            content: \"Please enter a valid domain name (e.g., example.com)\"\n        },\n        MX: {\n            content: \"Please enter a valid mail server domain (e.g., mail.example.com)\",\n            priority: \"Priority must be a number between 0 and 65535\"\n        },\n        TXT: {\n            content: \"Text content cannot be empty and must be less than 255 characters\"\n        },\n        NS: {\n            content: \"Please enter a valid nameserver domain (e.g., ns1.example.com)\"\n        },\n        SRV: {\n            name: \"Service name must be in format _service._protocol (e.g., _sip._tcp)\",\n            content: \"Please enter a valid target hostname (e.g., target.example.com)\",\n            priority: \"Priority must be a number between 0 and 65535\",\n            weight: \"Weight must be a number between 0 and 65535\",\n            port: \"Port must be a number between 1 and 65535\"\n        }\n    };\n    return ((_messages_type = messages[type]) === null || _messages_type === void 0 ? void 0 : _messages_type[field]) || \"Invalid value\";\n};\n// Check if a record type has restrictions for certain names\nconst checkRecordRestrictions = (type, name)=>{\n    const recordType = DNS_RECORD_TYPES[type];\n    if (!(recordType === null || recordType === void 0 ? void 0 : recordType.restrictions)) {\n        return {\n            isValid: true\n        };\n    }\n    const { nameCannotBe, message } = recordType.restrictions;\n    const isValid = !nameCannotBe.includes(name);\n    return {\n        isValid,\n        message: isValid ? null : message\n    };\n};\n// Get all available DNS record types as array\nconst getDnsRecordTypes = ()=>{\n    return Object.keys(DNS_RECORD_TYPES).map((key)=>({\n            value: key,\n            label: DNS_RECORD_TYPES[key].label,\n            description: DNS_RECORD_TYPES[key].description\n        }));\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/constants/dnsRecords.js\n"));

/***/ })

});
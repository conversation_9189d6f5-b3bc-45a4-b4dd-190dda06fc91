DNS SERVICE API DOCUMENTATION
=============================


1. ACTIVATE DNS SERVICE
-----------------------

Description:
Activates the DNS service for a given order.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : order-id
Data Type    : Integer
Required     : Yes
Description  : Order ID of the order to activate DNS service

Example Test URL Request:
https://test.httpapi.com/api/dns/activate.xml?auth-userid=0&api-key=key&order-id=0

Response:
Returns a map containing status information.


2. ADD IPv4 ADDRESS RECORD (A RECORD)
-------------------------------------

Description:
Adds an IPv4 (A) record for a domain.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : domain-name
Data Type    : String
Required     : Yes
Description  : Domain name for which to add the A record

Name         : value
Data Type    : String
Required     : Yes
Description  : The IPv4 address to be added

Name         : host
Data Type    : String
Required     : No
Description  : Hostname (e.g., "www" for www.domain.com)

Name         : ttl
Data Type    : Integer
Required     : No
Description  : Time-to-live in seconds (default: 14400)

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/add-ipv4-record.json?auth-userid=0&api-key=key&domain-name=domain.asia&value=0.0.0.0

Response:
Returns "Success" if the record is added successfully.
In case of error, returns "error" key with error description.


3. MODIFY IPv4 ADDRESS RECORD
-----------------------------

Description:
Modifies an existing IPv4 (A) record.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : domain-name
Data Type    : String
Required     : Yes
Description  : Domain name for which to modify the A record

Name         : host
Data Type    : String
Required     : No
Description  : Hostname (e.g., "www" for www.domain.com)

Name         : current-value
Data Type    : String
Required     : Yes
Description  : Current IPv4 address

Name         : new-value
Data Type    : String
Required     : Yes
Description  : New IPv4 address

Name         : ttl
Data Type    : Integer
Required     : No
Description  : Time-to-live in seconds (default: 14400)

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/update-ipv4-record.json?auth-userid=0&api-key=key&domain-name=domain.asia&current-value=0.0.0.0&new-value=*******

Response:
Returns "Success" if the record is modified successfully.
In case of error, returns "ERROR" with an error message.


4. DELETE IPv4 ADDRESS RECORD
-----------------------------

Description:
Deletes an existing IPv4 (A) record.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : domain-name
Data Type    : String
Required     : Yes
Description  : Domain name for which to delete the A record

Name         : value
Data Type    : String
Required     : Yes
Description  : IPv4 address to delete

Name         : host
Data Type    : String
Required     : No
Description  : Hostname (e.g., "www" for www.domain.com)

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/delete-ipv4-record.json?auth-userid=0&api-key=key&domain-name=domain.com&host=www&value=0.0.0.0

Response:
Returns "Success" if the record is deleted successfully or already does not exist.
In case of error, returns "error" key with error description.


5. SEARCH DNS RECORDS
----------------------

Description:
Searches DNS records based on specified criteria.

HTTP Method:
GET

Parameters:
-----------
Name            : auth-userid
Data Type       : Integer
Required        : Yes
Description     : Authentication Parameter

Name            : api-key
Data Type       : String
Required        : Yes
Description     : Authentication Parameter

Name            : domain-name
Data Type       : String
Required        : Yes
Description     : The domain name whose DNS records are being queried

Name            : type
Data Type       : String
Required        : Yes
Description     : Type of DNS record. Possible values:
                  A, MX, CNAME, TXT, NS, SRV, AAAA

Name            : no-of-records
Data Type       : Integer
Required        : Yes
Description     : Number of resource records to fetch

Name            : page-no
Data Type       : Integer
Required        : Yes
Description     : Page number for pagination

Name            : host
Data Type       : String
Required        : No
Description     : Hostname filter (optional)

Name            : value
Data Type       : String
Required        : No
Description     : Record value filter (optional)

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/search-records.json?auth-userid=0&api-key=key&domain-name=domain.asia&type=A&no-of-records=10&page-no=1

Response:
Returns details of the DNS records that match the specified criteria.

6. ADD IPv6 ADDRESS RECORD (AAAA RECORD)
----------------------------------------

Description:
Adds an IPv6 (AAAA) record for a domain.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : domain-name
Data Type    : String
Required     : Yes
Description  : Domain name for which to add the AAAA record

Name         : value
Data Type    : String
Required     : Yes
Description  : The IPv6 address to be added

Name         : host
Data Type    : String
Required     : No
Description  : Hostname (e.g., "www" for www.domain.com)

Name         : ttl
Data Type    : Integer
Required     : No
Description  : Time-to-live in seconds (default: 14400)

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/add-ipv6-record.json?auth-userid=0&api-key=key&domain-name=domain.asia&value=2001:252:0:1::2008:6

Response:
Returns "Success" if the record is added successfully.
In case of error, returns "error" key with error description.


7. MODIFY IPv6 ADDRESS RECORD
-----------------------------

Description:
Modifies an existing IPv6 (AAAA) record.

HTTP Method:
POST

Parameters:
-----------
Name           : auth-userid
Data Type      : Integer
Required       : Yes
Description    : Authentication Parameter

Name           : api-key
Data Type      : String
Required       : Yes
Description    : Authentication Parameter

Name           : domain-name
Data Type      : String
Required       : Yes
Description    : Domain name for which to modify the AAAA record

Name           : host
Data Type      : String
Required       : No
Description    : Hostname (e.g., "www" for www.domain.com)

Name           : current-value
Data Type      : String
Required       : Yes
Description    : Current IPv6 address

Name           : new-value
Data Type      : String
Required       : Yes
Description    : New IPv6 address

Name           : ttl
Data Type      : Integer
Required       : No
Description    : Time-to-live in seconds (default: 14400)

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/update-ipv6-record.json?auth-userid=0&api-key=key&domain-name=domain.asia&current-value=2001:252:0:1::2008:6&new-value=2001:252:0:1::2008:8

Response:
Returns "Success" if the record is modified successfully.
In case of error, returns "ERROR" with an error message.


8. DELETE IPv6 ADDRESS RECORD
-----------------------------

Description:
Deletes an existing IPv6 (AAAA) record.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : domain-name
Data Type    : String
Required     : Yes
Description  : Domain name for which to delete the AAAA record

Name         : host
Data Type    : String
Required     : Yes
Description  : Hostname of the record to be deleted

Name         : value
Data Type    : String
Required     : Yes
Description  : IPv6 address to delete

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/delete-ipv6-record.json?auth-userid=0&api-key=key&domain-name=domain.com&host=www&value=0.0.0.0

Response:
Returns "Success" if the record is deleted successfully or already does not exist.
In case of error, returns "error" key with error description.

9. ADD CNAME RECORD
--------------------

Description:
Adds a Canonical (CNAME) record.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : domain-name
Data Type    : String
Required     : Yes
Description  : Domain name for which to add the CNAME record

Name         : value
Data Type    : String
Required     : Yes
Description  : Fully Qualified Domain Name (FQDN) as the destination

Name         : host
Data Type    : String
Required     : No
Description  : Hostname (e.g., "www" for www.domain.com)

Name         : ttl
Data Type    : Integer
Required     : No
Description  : Time-to-live in seconds (default: 14400)

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/add-cname-record.json?auth-userid=0&api-key=key&domain-name=domain.asia&value=www.domain.com

Response:
Returns "Success" if the record is added successfully.
In case of error, returns "error" key with error description.


10. MODIFY CNAME RECORD
------------------------

Description:
Modifies a Canonical (CNAME) record.

HTTP Method:
POST

Parameters:
-----------
Name           : auth-userid
Data Type      : Integer
Required       : Yes
Description    : Authentication Parameter

Name           : api-key
Data Type      : String
Required       : Yes
Description    : Authentication Parameter

Name           : domain-name
Data Type      : String
Required       : Yes
Description    : Domain name for which to modify the CNAME record

Name           : host
Data Type      : String
Required       : No
Description    : Hostname (e.g., "www" for www.domain.com)

Name           : current-value
Data Type      : String
Required       : Yes
Description    : Current CNAME value

Name           : new-value
Data Type      : String
Required       : Yes
Description    : New CNAME value

Name           : ttl
Data Type      : Integer
Required       : No
Description    : Time-to-live in seconds (default: 14400)

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/update-cname-record.json?auth-userid=0&api-key=key&domain-name=domain.asia&current-value=blog.domain.com&new-value=wp.domain.com

Response:
Returns "Success" if the record is modified successfully.
In case of error, returns "ERROR" with an error message.


11. DELETE CNAME RECORD
------------------------

Description:
Deletes a Canonical (CNAME) record.

HTTP Method:
POST

Parameters:
-----------
Name         : auth-userid
Data Type    : Integer
Required     : Yes
Description  : Authentication Parameter

Name         : api-key
Data Type    : String
Required     : Yes
Description  : Authentication Parameter

Name         : domain-name
Data Type    : String
Required     : Yes
Description  : Domain name for which to delete the CNAME record

Name         : host
Data Type    : String
Required     : Yes
Description  : Hostname of the record to be deleted

Name         : value
Data Type    : String
Required     : Yes
Description  : Fully Qualified Domain Name (FQDN) of the CNAME record

Example Test URL Request:
https://test.httpapi.com/api/dns/manage/delete-cname-record.json?auth-userid=0&api-key=key&domain-name=domain.com&host=webmail&value=webmail.domain.com

Response:
Returns "Success" if the record is deleted successfully or already does not exist.
In case of error, returns "error" key with error description.

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx":
/*!***************************************************!*\
  !*** ./src/components/domains/DnsRecordTable.jsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DnsRecordTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/dnsRecords */ \"(app-pages-browser)/./src/constants/dnsRecords.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DnsRecordTable(param) {\n    let { records, recordType, onEdit, onDelete, onAdd, domain, loading = false } = param;\n    _s();\n    const [deleteConfirm, setDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInlineForm, setShowInlineForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Reset form state when record type changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowInlineForm(false);\n        setFormData({});\n        setFormErrors({});\n    }, [\n        recordType\n    ]);\n    // Initialize form data for the current record type\n    const initializeFormData = (type)=>{\n        const recordTypeConfig = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DNS_RECORD_TYPES[type];\n        if (!recordTypeConfig) return {\n            type\n        };\n        const initialData = {\n            type\n        };\n        recordTypeConfig.fields.forEach((field)=>{\n            if (field.name === \"ttl\") {\n                initialData[field.name] = field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL;\n            } else if (field.type === \"number\") {\n                initialData[field.name] = field.default || 0;\n            } else {\n                initialData[field.name] = field.default || \"\";\n            }\n        });\n        return initialData;\n    };\n    // Handle add button click - shows inline form\n    const handleAddClick = ()=>{\n        const initialData = initializeFormData(recordType);\n        setFormData(initialData);\n        setFormErrors({});\n        setShowInlineForm(true);\n    };\n    // Handle form field changes\n    const handleFieldChange = (fieldName, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [fieldName]: value\n            }));\n        // Clear error for this field\n        if (formErrors[fieldName]) {\n            setFormErrors((prev)=>({\n                    ...prev,\n                    [fieldName]: null\n                }));\n        }\n    };\n    // Handle form submission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            // Validate the form data\n            const validation = (0,_constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.validateDnsRecord)(formData);\n            if (!validation.isValid) {\n                setFormErrors(validation.errors);\n                return;\n            }\n            await onAdd(formData);\n            setShowInlineForm(false);\n            setFormData({});\n            setFormErrors({});\n        } catch (error) {\n            console.error(\"Error adding record:\", error);\n        // Handle validation errors if needed\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Handle form cancel\n    const handleCancel = ()=>{\n        setShowInlineForm(false);\n        setFormData({});\n        setFormErrors({});\n    };\n    // Get record type configuration\n    const getRecordTypeConfig = (type)=>{\n        const configs = {\n            A: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                label: \"IPv4 Address\",\n                color: \"blue\",\n                bgColor: \"bg-blue-50\",\n                textColor: \"text-blue-600\",\n                borderColor: \"border-blue-200\"\n            },\n            AAAA: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                label: \"IPv6 Address\",\n                color: \"purple\",\n                bgColor: \"bg-purple-50\",\n                textColor: \"text-purple-600\",\n                borderColor: \"border-purple-200\"\n            },\n            CNAME: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                label: \"Canonical Name\",\n                color: \"green\",\n                bgColor: \"bg-green-50\",\n                textColor: \"text-green-600\",\n                borderColor: \"border-green-200\"\n            },\n            MX: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                label: \"Mail Exchange\",\n                color: \"orange\",\n                bgColor: \"bg-orange-50\",\n                textColor: \"text-orange-600\",\n                borderColor: \"border-orange-200\"\n            },\n            TXT: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                label: \"Text Record\",\n                color: \"gray\",\n                bgColor: \"bg-gray-50\",\n                textColor: \"text-gray-600\",\n                borderColor: \"border-gray-200\"\n            },\n            NS: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                label: \"Name Server\",\n                color: \"indigo\",\n                bgColor: \"bg-indigo-50\",\n                textColor: \"text-indigo-600\",\n                borderColor: \"border-indigo-200\"\n            },\n            SRV: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                label: \"Service Record\",\n                color: \"pink\",\n                bgColor: \"bg-pink-50\",\n                textColor: \"text-pink-600\",\n                borderColor: \"border-pink-200\"\n            }\n        };\n        return configs[type] || configs.A;\n    };\n    // Format TTL display\n    const formatTTL = (ttl)=>{\n        const numericTTL = parseInt(ttl);\n        const option = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.TTL_OPTIONS.find((opt)=>opt.value === numericTTL);\n        if (option) {\n            return option.label;\n        }\n        // Format custom TTL values\n        if (numericTTL >= 86400) {\n            const days = Math.floor(numericTTL / 86400);\n            const remainder = numericTTL % 86400;\n            if (remainder === 0) {\n                return \"\".concat(days, \" day\").concat(days !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 3600) {\n            const hours = Math.floor(numericTTL / 3600);\n            const remainder = numericTTL % 3600;\n            if (remainder === 0) {\n                return \"\".concat(hours, \" hour\").concat(hours !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 60) {\n            const minutes = Math.floor(numericTTL / 60);\n            const remainder = numericTTL % 60;\n            if (remainder === 0) {\n                return \"\".concat(minutes, \" minute\").concat(minutes !== 1 ? \"s\" : \"\");\n            }\n        }\n        return \"\".concat(numericTTL, \"s\");\n    };\n    // Format record name\n    const formatRecordName = (name)=>{\n        if (!name || name === \"@\") return (domain === null || domain === void 0 ? void 0 : domain.name) || \"@\";\n        if (name.endsWith(\".\")) return name.slice(0, -1);\n        return name;\n    };\n    // Copy to clipboard\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n        } catch (err) {\n            console.error(\"Failed to copy:\", err);\n        }\n    };\n    // Handle delete confirmation\n    const handleDeleteClick = (record)=>{\n        setDeleteConfirm(record);\n    };\n    const handleDeleteConfirm = ()=>{\n        if (deleteConfirm && onDelete) {\n            onDelete(deleteConfirm.id, deleteConfirm);\n        }\n        setDeleteConfirm(null);\n    };\n    const config = getRecordTypeConfig(recordType);\n    const IconComponent = config.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 rounded-lg \".concat(config.bgColor, \" border border-gray-200\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"h-5 w-5 \".concat(config.textColor)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: [\n                                                    recordType,\n                                                    \" Records\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    config.label,\n                                                    \" • \",\n                                                    records.length,\n                                                    \" record\",\n                                                    records.length !== 1 ? \"s\" : \"\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this),\n                            !showInlineForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAddClick,\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add \",\n                                    recordType\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    showInlineForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-medium text-blue-900\",\n                                            children: [\n                                                \"Add New \",\n                                                recordType,\n                                                \" Record\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this),\n                                (()=>{\n                                    const recordTypeConfig = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DNS_RECORD_TYPES[recordType];\n                                    if (!recordTypeConfig) return null;\n                                    const fields = recordTypeConfig.fields;\n                                    const gridCols = fields.length <= 3 ? \"md:grid-cols-\".concat(fields.length) : \"md:grid-cols-3\";\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 \".concat(gridCols, \" gap-4\"),\n                                        children: fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: [\n                                                            field.label,\n                                                            field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 ml-1\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 46\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    field.type === \"select\" && field.name === \"ttl\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData[field.name] || field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL,\n                                                        onChange: (e)=>handleFieldChange(field.name, parseInt(e.target.value)),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                        children: _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.TTL_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: option.value,\n                                                                children: option.label\n                                                            }, option.value, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 31\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 27\n                                                    }, this) : field.type === \"number\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, parseInt(e.target.value) || 0),\n                                                        placeholder: field.placeholder,\n                                                        min: field.min,\n                                                        max: field.max,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 27\n                                                    }, this) : field.type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                                                        placeholder: field.placeholder,\n                                                        rows: 2,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                                                        placeholder: field.placeholder,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    formErrors[field.name] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: formErrors[field.name]\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 19\n                                    }, this);\n                                })(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between pt-4 border-t border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: [\n                                                'Use \"@\" for root domain (',\n                                                domain === null || domain === void 0 ? void 0 : domain.name,\n                                                '), \"www\" for www.',\n                                                domain === null || domain === void 0 ? void 0 : domain.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleCancel,\n                                                    disabled: isSubmitting,\n                                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 inline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Cancel\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isSubmitting,\n                                                    className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"animate-spin h-4 w-4 mr-2 inline\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        className: \"opacity-25\",\n                                                                        cx: \"12\",\n                                                                        cy: \"12\",\n                                                                        r: \"10\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        className: \"opacity-75\",\n                                                                        fill: \"currentColor\",\n                                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 379,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Adding...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2 inline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Add Record\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 283,\n                        columnNumber: 11\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, this),\n                                \"Loading \",\n                                recordType,\n                                \" records...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 399,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 398,\n                        columnNumber: 11\n                    }, this) : records.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 rounded-full \".concat(config.bgColor),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-8 w-8 \".concat(config.textColor)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: [\n                                                \"No \",\n                                                recordType,\n                                                \" records found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: [\n                                                \"Create your first \",\n                                                recordType,\n                                                \" record to get started\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddClick,\n                                            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add \",\n                                                recordType,\n                                                \" Record\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 409,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 408,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"TTL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 19\n                                            }, this),\n                                            (recordType === \"MX\" || recordType === \"SRV\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Priority\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"relative px-6 py-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: records.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50 transition-colors duration-150\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: formatRecordName(record.name)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-mono text-gray-700 flex-1 min-w-0 truncate\",\n                                                                children: record.content\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>copyToClipboard(record.content),\n                                                                className: \"p-1 text-gray-400 hover:text-gray-600 rounded\",\n                                                                title: \"Copy to clipboard\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 480,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            formatTTL(record.ttl)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 21\n                                                }, this),\n                                                (recordType === \"MX\" || recordType === \"SRV\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-900\",\n                                                        children: record.priority || \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-end gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>onEdit && onEdit(record),\n                                                                className: \"p-2 text-gray-400 hover:text-blue-600 rounded-md hover:bg-blue-50\",\n                                                                title: \"Edit record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleDeleteClick(record),\n                                                                className: \"p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-red-50\",\n                                                                title: \"Delete record\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 503,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, record.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 432,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 431,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            deleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center sm:text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg leading-6 font-medium text-gray-900\",\n                                            children: \"Delete DNS Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 534,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mb-4\",\n                                        children: [\n                                            \"Are you sure you want to delete this \",\n                                            deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.type,\n                                            \" record? This action cannot be undone.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-gray-50 rounded-lg border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                    children: formatRecordName(deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.name)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-mono text-gray-600\",\n                                                    children: deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.content\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 544,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDeleteConfirm(null),\n                                        className: \"flex-1 px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 560,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDeleteConfirm,\n                                        className: \"flex-1 px-4 py-2 bg-red-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                        children: \"Delete Record\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 559,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 533,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                    lineNumber: 532,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 531,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DnsRecordTable, \"EMPfo3qgCdukLUyhx/J9sXiisRk=\");\n_c = DnsRecordTable;\nvar _c;\n$RefreshReg$(_c, \"DnsRecordTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\n"));

/***/ })

});
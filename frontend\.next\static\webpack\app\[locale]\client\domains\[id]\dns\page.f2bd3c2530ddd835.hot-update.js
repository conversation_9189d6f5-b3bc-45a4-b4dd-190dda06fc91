"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/dns/page.jsx":
/*!***********************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/dns/page.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDnsManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,FileText,Globe,Info,Mail,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_ImprovedDnsManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/domains/ImprovedDnsManager */ \"(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction DomainDnsManagementPage() {\n    _s();\n    const { id } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"records\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to update domain data\n    const updateDomain = (updatedDomain)=>{\n        setDomain((prevDomain)=>({\n                ...prevDomain,\n                ...updatedDomain\n            }));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchDomainData = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Get domain data from user domains\n                const domainsResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserDomains();\n                if (domainsResponse.data && domainsResponse.data.domains) {\n                    const foundDomain = domainsResponse.data.domains.find((d)=>d.id === id);\n                    if (!foundDomain) {\n                        setError(\"Domain not found\");\n                        return;\n                    }\n                    // Get detailed domain information\n                    try {\n                        const detailsResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getDomainDetailsByName(foundDomain.name, \"All\");\n                        if (detailsResponse.data.success && detailsResponse.data.domain) {\n                            const domainDetails = detailsResponse.data.domain;\n                            // Merge domain data with details\n                            const enrichedDomain = {\n                                ...foundDomain,\n                                ...domainDetails,\n                                // Ensure we keep the original ID\n                                id: foundDomain.id\n                            };\n                            setDomain(enrichedDomain);\n                        } else {\n                            // Use basic domain data if details fetch fails\n                            setDomain(foundDomain);\n                        }\n                    } catch (detailsError) {\n                        console.warn(\"Could not fetch domain details:\", detailsError);\n                        // Use basic domain data if details fetch fails\n                        setDomain(foundDomain);\n                    }\n                } else {\n                    setError(\"Failed to load domain data\");\n                }\n            } catch (error) {\n                console.error(\"Error fetching domain data:\", error);\n                setError(\"Failed to load domain information\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load domain information\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (id) {\n            fetchDomainData();\n        }\n    }, [\n        id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.orderid) {\n            console.log(\"Order ID available:\", domain.orderid);\n        } else {\n            console.warn(\"Order ID is missing for domain:\", domain);\n        }\n    }, [\n        domain\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-[60vh]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                className: \"text-gray-600\",\n                                children: t(\"loading\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 136,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 135,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8 bg-gray-50 min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"text\",\n                        className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                        onClick: ()=>router.push(\"/client/domains\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            dt(\"back_to_domains\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                        color: \"red\",\n                        className: \"max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            error || \"Domain not found\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Breadcrumbs, {\n                                    className: \"bg-white py-2 px-3 rounded-lg border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"text\",\n                                            className: \"text-blue-600 hover:text-blue-800 p-0\",\n                                            onClick: ()=>router.push(\"/client/domains\"),\n                                            children: t(\"domains\", {\n                                                defaultValue: \"Domains\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                            className: \"text-gray-600\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-gray-800 font-medium\",\n                                                    children: t(\"dns_management\", {\n                                                        defaultValue: \"DNS Management\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full \".concat(domain.status === \"active\" || domain.currentstatus === \"Active\" ? \"bg-green-500\" : domain.status === \"pending\" ? \"bg-yellow-500\" : \"bg-gray-400\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    variant: \"h4\",\n                                                    className: \"text-gray-900\",\n                                                    children: domain.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this),\n                                                domain.domainOrderId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"ID: \",\n                                                        domain.domainOrderId\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    content: \"Domain Security\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-green-50 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    content: \"DNS Status\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-blue-50 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                            className: \"text-gray-600\",\n                            children: t(\"manage_dns_settings_description\", {\n                                defaultValue: \"Configure DNS records and nameservers for your domain. Changes may take up to 24-48 hours to propagate globally.\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-white border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsHeader, {\n                                    className: \"bg-gray-50 border-b\",\n                                    indicatorProps: {\n                                        className: \"bg-transparent\"\n                                    },\n                                    children: [\n                                        {\n                                            label: \"DNS Records\",\n                                            value: \"records\",\n                                            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                            description: \"Set up where your domain points to\"\n                                        },\n                                        {\n                                            label: \"Nameservers\",\n                                            value: \"nameservers\",\n                                            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                            description: \"Configure your domain's DNS servers\"\n                                        }\n                                    ].map((param)=>{\n                                        let { label, value, icon: Icon, description } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                            value: value,\n                                            className: \"flex items-center justify-center gap-2 px-6 py-3 relative text-gray-600 hover:text-gray-900\",\n                                            onClick: ()=>setActiveTab(value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center gap-2 \".concat(value === activeTab ? \"text-blue-500\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, this),\n                                                value === activeTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 w-full h-0.5 bg-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, value, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsBody, {\n                                    className: \"relative min-h-[400px]\",\n                                    children: [\n                                        {\n                                            value: \"records\",\n                                            title: \"DNS Records\",\n                                            description: \"DNS records tell the internet where to find your website, email, and other services\",\n                                            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_ImprovedDnsManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: updateDomain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 23\n                                            }, this)\n                                        },\n                                        {\n                                            value: \"nameservers\",\n                                            title: \"Nameservers\",\n                                            description: \"Nameservers are the internet's address book for your domain\",\n                                            guide: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 mb-6 bg-blue-50 border border-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-blue-100 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-blue-900 mb-1\",\n                                                                    children: \"About Nameservers\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"text-sm text-blue-800\",\n                                                                    children: \"Nameservers control who manages your domain's DNS settings. Only change these if you're moving to a different DNS provider.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 23\n                                            }, this),\n                                            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: updateDomain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 23\n                                            }, this)\n                                        }\n                                    ].map((param)=>{\n                                        let { value, title, description, guide, component } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                            value: value,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            guide\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    component\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, value, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                            lineNumber: 238,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 bg-blue-50 border border-blue-100 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 text-blue-600 mt-0.5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                        className: \"font-medium text-blue-900 mb-2\",\n                                        children: \"Important DNS Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm space-y-2 text-blue-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"DNS changes take 24-48 hours to propagate globally\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Always backup your current DNS settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_FileText_Globe_Info_Mail_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Test your DNS changes using online tools\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDnsManagementPage, \"PPmgeJW0xHd6YO6+cvmPTeSqUBY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations\n    ];\n});\n_c = DomainDnsManagementPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDnsManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/dns/page.jsx\n"));

/***/ })

});
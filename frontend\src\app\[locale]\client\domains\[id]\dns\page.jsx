"use client";
import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import {
  Typography,
  Card,
  CardBody,
  Button,
  Tabs,
  TabsHeader,
  TabsBody,
  Tab,
  TabPanel,
  Alert,
  Breadcrumbs,
  Tooltip,
} from "@material-tailwind/react";
import {
  ArrowLeft,
  Server,
  Globe,
  Shield,
  AlertCircle,
  Zap,
  Activity,
  Info,
  Mail,
  FileText,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import ImprovedDnsManager from "@/components/domains/ImprovedDnsManager";
import { toast } from "react-toastify";
import NameserverManager from "@/components/domains/NameserverManager";

import axios from "axios";

export default function DomainDnsManagementPage() {
  const { id } = useParams();
  const router = useRouter();
  const t = useTranslations("client");
  const dt = useTranslations("client.domainWrapper");

  const [domain, setDomain] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("records");
  const [error, setError] = useState(null);

  // Function to update domain data
  const updateDomain = (updatedDomain) => {
    setDomain((prevDomain) => ({
      ...prevDomain,
      ...updatedDomain,
    }));
  };

  useEffect(() => {
    const fetchDomainData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get domain data from user domains
        const domainsResponse = await domainMngService.getUserDomains();

        if (domainsResponse.data && domainsResponse.data.domains) {
          const foundDomain = domainsResponse.data.domains.find(
            (d) => d.id === id
          );

          if (!foundDomain) {
            setError("Domain not found");
            return;
          }

          // Get detailed domain information
          try {
            const detailsResponse =
              await domainMngService.getDomainDetailsByName(
                foundDomain.name,
                "All"
              );

            if (detailsResponse.data.success && detailsResponse.data.domain) {
              const domainDetails = detailsResponse.data.domain;

              // Merge domain data with details
              const enrichedDomain = {
                ...foundDomain,
                ...domainDetails,
                // Ensure we keep the original ID
                id: foundDomain.id,
              };


              setDomain(enrichedDomain);
            } else {
              // Use basic domain data if details fetch fails
              setDomain(foundDomain);
            }
          } catch (detailsError) {
            console.warn("Could not fetch domain details:", detailsError);
            // Use basic domain data if details fetch fails
            setDomain(foundDomain);
          }
        } else {
          setError("Failed to load domain data");
        }
      } catch (error) {
        console.error("Error fetching domain data:", error);
        setError("Failed to load domain information");
        toast.error("Failed to load domain information");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchDomainData();
    }
  }, [id]);

  useEffect(() => {
    if (domain?.orderid) {
      console.log("Order ID available:", domain.orderid);
    } else {
      console.warn("Order ID is missing for domain:", domain);
    }
  }, [domain]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto p-4">
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="flex items-center gap-3">
              <div className="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full"></div>
              <Typography className="text-gray-600">{t("loading")}</Typography>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !domain) {
    return (
      <div className="p-8 bg-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <Button
            variant="text"
            className="mb-6 text-blue-600 flex items-center gap-2"
            onClick={() => router.push("/client/domains")}
          >
            <ArrowLeft className="h-4 w-4" />
            {dt("back_to_domains")}
          </Button>

          <Alert color="red" className="max-w-md mx-auto">
            <AlertCircle className="h-4 w-4" />
            {error || "Domain not found"}
          </Alert>
        </div>
      </div>
    );
  }


  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-4">
        <div className="max-w-7xl mx-auto">
          {/* Navigation */}
          <div className="mb-8">
            <Breadcrumbs className="bg-white py-2 px-3 rounded-lg border">
              <Button
                variant="text"
                className="text-blue-600 hover:text-blue-800 p-0 font-normal"
                onClick={() => router.push("/")}
              >
                Portal Home
              </Button>
              <Button
                variant="text"
                className="text-blue-600 hover:text-blue-800 p-0 font-normal"
                onClick={() => router.push("/client")}
              >
                Client Area
              </Button>
              <Button
                variant="text"
                className="text-blue-600 hover:text-blue-800 p-0 font-normal"
                onClick={() => router.push("/client/domains")}
              >
                My Domains
              </Button>
              <Button
                variant="text"
                className="text-blue-600 hover:text-blue-800 p-0 font-normal"
                onClick={() => router.push(`/client/domains/${id}`)}
              >
                {domain.name}
              </Button>
              <div className="flex items-center gap-2">
                <Typography className="text-gray-800 font-medium">
                  Manage DNS Zone
                </Typography>
                <div
                  className={`h-2 w-2 rounded-full ${
                    domain.status === "active" ||
                    domain.currentstatus === "Active"
                      ? "bg-green-500"
                      : domain.status === "pending"
                      ? "bg-yellow-500"
                      : "bg-gray-400"
                  }`}
                />
              </div>
            </Breadcrumbs>

            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center gap-4">
                <Typography variant="h4" className="text-gray-900">
                  {domain.name}
                </Typography>
                {domain.domainOrderId && (
                  <Typography className="text-sm text-gray-500">
                    ID: {domain.domainOrderId}
                  </Typography>
                )}
              </div>
              <div className="flex items-center gap-3">
                <Tooltip content="Domain Security">
                  <div className="p-2 bg-green-50 rounded-lg">
                    <Shield className="h-4 w-4 text-green-600" />
                  </div>
                </Tooltip>
                <Tooltip content="DNS Status">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <Activity className="h-4 w-4 text-blue-600" />
                  </div>
                </Tooltip>
              </div>
            </div>
          </div>

          <Typography className="text-gray-600">
            {t("manage_dns_settings_description", {
              defaultValue:
                "Configure DNS records and nameservers for your domain. Changes may take up to 24-48 hours to propagate globally.",
            })}
          </Typography>
        </div>

        {/* DNS Management Tabs */}
        <Card className="bg-white border">
          <CardBody className="p-0">
            <Tabs>
              <TabsHeader
                className="bg-gray-50 border-b"
                indicatorProps={{
                  className: "bg-transparent",
                }}
              >
                {[
                  {
                    label: "DNS Records",
                    value: "records",
                    icon: Globe,
                    description: "Set up where your domain points to",
                  },
                  {
                    label: "Nameservers",
                    value: "nameservers",
                    icon: Server,
                    description: "Configure your domain's DNS servers",
                  },
                ].map(({ label, value, icon: Icon, description }) => (
                  <Tab
                    key={value}
                    value={value}
                    className="flex items-center justify-center gap-2 px-6 py-3 relative text-gray-600 hover:text-gray-900"
                    onClick={() => setActiveTab(value)}
                  >
                    <div
                      className={`flex items-center justify-center gap-2 ${
                        value === activeTab ? "text-blue-500" : ""
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      <span>{label}</span>
                    </div>
                    {value === activeTab && (
                      <div className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-500" />
                    )}
                  </Tab>
                ))}
              </TabsHeader>

              <TabsBody className="relative min-h-[400px]">
                {[
                  {
                    value: "records",
                    title: "DNS Records",
                    description:
                      "DNS records tell the internet where to find your website, email, and other services",
                    guide: (
                      <div className="grid grid-cols-2 gap-3 p-4 bg-gray-50 rounded-lg mb-6">
                        <div className="p-3 bg-white rounded border border-gray-200">
                          <div className="flex items-center gap-2 mb-2 text-blue-700">
                            <Globe className="h-4 w-4" />
                            <span className="font-medium">A Record</span>
                          </div>
                          <p className="text-sm text-gray-600">
                            Connect to web hosting (IPv4)
                          </p>
                        </div>
                        <div className="p-3 bg-white rounded border border-gray-200">
                          <div className="flex items-center gap-2 mb-2 text-purple-700">
                            <Server className="h-4 w-4" />
                            <span className="font-medium">CNAME</span>
                          </div>
                          <p className="text-sm text-gray-600">
                            Point to another domain
                          </p>
                        </div>
                        <div className="p-3 bg-white rounded border border-gray-200">
                          <div className="flex items-center gap-2 mb-2 text-green-700">
                            <Mail className="h-4 w-4" />
                            <span className="font-medium">MX Record</span>
                          </div>
                          <p className="text-sm text-gray-600">
                            Set up email servers
                          </p>
                        </div>
                        <div className="p-3 bg-white rounded border border-gray-200">
                          <div className="flex items-center gap-2 mb-2 text-orange-700">
                            <FileText className="h-4 w-4" />
                            <span className="font-medium">TXT Record</span>
                          </div>
                          <p className="text-sm text-gray-600">
                            Verify domain ownership
                          </p>
                        </div>
                      </div>
                    ),
                    component: (
                      <ImprovedDnsManager
                        domain={domain}
                        onUpdate={updateDomain}
                      />
                    ),
                  },
                  {
                    value: "nameservers",
                    title: "Nameservers",
                    description:
                      "Nameservers are the internet's address book for your domain",
                    guide: (
                      <div className="p-4 mb-6 bg-blue-50 border border-blue-100 rounded-lg">
                        <div className="flex items-start gap-3">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <Server className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <Typography className="font-medium text-blue-900 mb-1">
                              About Nameservers
                            </Typography>
                            <Typography className="text-sm text-blue-800">
                              Nameservers control who manages your domain's DNS
                              settings. Only change these if you're moving to a
                              different DNS provider.
                            </Typography>
                          </div>
                        </div>
                      </div>
                    ),
                    component: (
                      <NameserverManager
                        domain={domain}
                        onUpdate={updateDomain}
                      />
                    ),
                  },
                ].map(({ value, title, description, guide, component }) => (
                  <TabPanel key={value} value={value}>
                    <div className="p-6">
                      <div className="mb-6">
                        <div className="flex items-center gap-2 mb-2">
                          <Typography variant="h4" className="text-gray-800">
                            {title}
                          </Typography>
                          <Tooltip content={description}>
                            <Info className="h-5 w-5 text-blue-500" />
                          </Tooltip>
                        </div>
                        {guide}
                      </div>
                      {component}
                    </div>
                  </TabPanel>
                ))}
              </TabsBody>
            </Tabs>
          </CardBody>
        </Card>

        {/* Quick Help */}
        <div className="mt-6 bg-blue-50 border border-blue-100 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <Typography className="font-medium text-blue-900 mb-2">
                Important DNS Information
              </Typography>
              <ul className="text-sm space-y-2 text-blue-800">
                <li className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  DNS changes take 24-48 hours to propagate globally
                </li>
                <li className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Always backup your current DNS settings
                </li>
                <li className="flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Use Health Check to verify your setup
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

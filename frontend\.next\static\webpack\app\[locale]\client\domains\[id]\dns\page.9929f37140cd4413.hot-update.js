"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx":
/*!*******************************************************!*\
  !*** ./src/components/domains/ImprovedDnsManager.jsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ImprovedDnsManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,FileText,Globe,Link,Mail,RefreshCw,Server,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./DnsRecordTable */ \"(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\");\n/* harmony import */ var _DnsRecordForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./DnsRecordForm */ \"(app-pages-browser)/./src/components/domains/DnsRecordForm.jsx\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ImprovedDnsManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [dnsServiceActive, setDnsServiceActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    const [allDnsRecords, setAllDnsRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activatingService, setActivatingService] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedRecordType, setSelectedRecordType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"A\");\n    // DNS record types configuration\n    const recordTypes = [\n        {\n            type: \"A\",\n            label: \"A\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"IPv4 addresses\"\n        },\n        {\n            type: \"AAAA\",\n            label: \"AAAA\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            description: \"IPv6 addresses\"\n        },\n        {\n            type: \"CNAME\",\n            label: \"CNAME\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Canonical names\"\n        },\n        {\n            type: \"MX\",\n            label: \"MX\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Mail servers\"\n        },\n        {\n            type: \"TXT\",\n            label: \"TXT\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Text records\"\n        },\n        {\n            type: \"NS\",\n            label: \"NS\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Name servers\"\n        },\n        {\n            type: \"SRV\",\n            label: \"SRV\",\n            icon: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Service records\"\n        }\n    ];\n    // Load DNS records\n    const loadDnsRecords = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D Loading DNS records for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name, \" (ID: \").concat(domain === null || domain === void 0 ? void 0 : domain.id, \")\"));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getDnsRecords(domain.id);\n            console.log(\"\\uD83D\\uDCCB DNS Records Response:\", response.data);\n            if (response.data.success) {\n                const records = response.data.records || [];\n                setAllDnsRecords(records);\n                console.log(\"✅ Loaded \".concat(records.length, \" DNS records\"));\n                // Don't automatically set DNS service as active just because records loaded\n                // The activation status should come from the domain data, not from successful record loading\n                // Log records by type for debugging\n                recordTypes.forEach((param)=>{\n                    let { type } = param;\n                    const typeRecords = records.filter((r)=>r.type === type);\n                    console.log(\"\\uD83D\\uDCCA \".concat(type, \" Records (\").concat(typeRecords.length, \"):\"), typeRecords);\n                });\n            } else {\n                throw new Error(response.data.error || \"Failed to load DNS records\");\n            }\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"❌ Error loading DNS records:\", error);\n            console.error(\"❌ Error details:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n            // Don't change DNS service activation status based on record loading failure\n            // The activation status should only be determined by domain.dnsActivated\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to load DNS records. Please check if DNS service is activated.\");\n            setAllDnsRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Activate DNS service\n    const activateDnsService = async ()=>{\n        try {\n            setActivatingService(true);\n            console.log(\"\\uD83D\\uDE80 Activating DNS service for domain: \".concat(domain === null || domain === void 0 ? void 0 : domain.name));\n            console.log(\"\\uD83D\\uDD0D Domain object:\", domain);\n            // The backend expects orderId, which should be the domain's order ID\n            const orderId = domain.orderid || domain.domainOrderId || domain.id;\n            console.log(\"\\uD83D\\uDCCB Using order ID: \".concat(orderId));\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].activateDnsService(orderId);\n            console.log(\"\\uD83D\\uDCCB DNS Activation Response:\", response.data);\n            if (response.data.success) {\n                setDnsServiceActive(true);\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"DNS service activated successfully!\");\n                // Update the domain object with DNS activation status\n                if (onUpdate) {\n                    onUpdate({\n                        dnsActivated: true,\n                        dnsActivatedAt: new Date().toISOString(),\n                        dnsZoneId: response.data.zoneId || null\n                    });\n                }\n                await loadDnsRecords(); // Load records after activation\n            } else {\n                throw new Error(response.data.error || \"Failed to activate DNS service\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error activating DNS service:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to activate DNS service\");\n        } finally{\n            setActivatingService(false);\n        }\n    };\n    // Add DNS record\n    const handleAddRecord = async (recordData)=>{\n        try {\n            console.log(\"➕ Adding DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].addDnsRecord(domain.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Add Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"\".concat(recordData.type, \" record added successfully!\"));\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to add DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error adding DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to add DNS record\");\n        }\n    };\n    // Edit DNS record\n    const handleEditRecord = async (recordData)=>{\n        try {\n            console.log(\"✏️ Editing DNS record:\", recordData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].updateDnsRecord(domain.id, recordData.id, recordData);\n            console.log(\"\\uD83D\\uDCCB Edit Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"\".concat(recordData.type, \" record updated successfully!\"));\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to update DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error updating DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to update DNS record\");\n        }\n    };\n    // Delete DNS record\n    const handleDeleteRecord = async function(recordId) {\n        let record = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null;\n        try {\n            console.log(\"\\uD83D\\uDDD1️ Deleting DNS record ID: \".concat(recordId), record);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].deleteDnsRecord(domain.id, recordId, record);\n            console.log(\"\\uD83D\\uDCCB Delete Record Response:\", response.data);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.success(\"DNS record deleted successfully!\");\n                await loadDnsRecords(); // Reload records\n            } else {\n                throw new Error(response.data.error || \"Failed to delete DNS record\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error deleting DNS record:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_5__.toast.error(\"Failed to delete DNS record\");\n        }\n    };\n    // Get records for specific type (filter out empty records)\n    const getRecordsForType = (type)=>{\n        return allDnsRecords.filter((record)=>record.type === type && record.content && record.content.trim() !== \"\");\n    };\n    // Update DNS service status when domain changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setDnsServiceActive((domain === null || domain === void 0 ? void 0 : domain.dnsActivated) || false);\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.dnsActivated\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.id) {\n            loadDnsRecords();\n        }\n    }, [\n        domain === null || domain === void 0 ? void 0 : domain.id\n    ]);\n    if (!domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-5 w-5 text-amber-600 flex-shrink-0\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 240,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-amber-800\",\n                    children: \"Domain information is required to manage DNS records.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n            lineNumber: 239,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-7xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-gray-900\",\n                                    children: \"DNS Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 252,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Manage DNS records for\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: dnsServiceActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-green-600 font-medium\",\n                                                        children: \"DNS Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 text-amber-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-amber-600 font-medium\",\n                                                        children: \"DNS Inactive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: loadDnsRecords,\n                                disabled: loading,\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(loading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-amber-50 border border-amber-200 rounded-lg p-6 mx-6 mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-amber-900 mb-1\",\n                                            children: \"DNS Service Not Active\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-amber-800 text-sm leading-relaxed\",\n                                            children: \"You need to activate DNS service before you can manage DNS records for this domain. Once activated, you'll be able to add, edit, and delete DNS records.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: activateDnsService,\n                            disabled: activatingService,\n                            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0\",\n                            children: [\n                                activatingService && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 17\n                                }, this),\n                                \"Activate DNS\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                    lineNumber: 290,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 289,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 pb-6\",\n                children: [\n                    !dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 h-20 bg-amber-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_FileText_Globe_Link_Mail_RefreshCw_Server_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-10 w-10 text-amber-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"max-w-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-medium text-gray-900 mb-3\",\n                                                children: \"DNS Management Not Available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 leading-relaxed\",\n                                                children: \"DNS service is not activated for this domain. Please activate DNS service using the button above to start managing your DNS records.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 326,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this),\n                    dnsServiceActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white border border-gray-200 rounded-lg shadow-sm mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-8 px-6\",\n                                    \"aria-label\": \"Tabs\",\n                                    children: recordTypes.map((param)=>{\n                                        let { type, label } = param;\n                                        const count = getRecordsForType(type).length;\n                                        const isActive = activeTab === type;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(type),\n                                            className: \"\".concat(isActive ? \"border-blue-500 text-blue-600\" : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\", \" whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 23\n                                                }, this),\n                                                count > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(isActive ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: count\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, type, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 348,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    records: getRecordsForType(activeTab),\n                                    recordType: activeTab,\n                                    onEdit: handleEditRecord,\n                                    onDelete: handleDeleteRecord,\n                                    onAdd: handleAddRecord,\n                                    domain: domain,\n                                    loading: loading\n                                }, activeTab, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            dnsServiceActive && editingRecord && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DnsRecordForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: !!editingRecord,\n                onClose: ()=>{\n                    setEditingRecord(null);\n                    setSelectedRecordType(\"A\");\n                },\n                onSubmit: handleEditRecord,\n                initialData: editingRecord,\n                domain: domain,\n                selectedType: selectedRecordType,\n                onTypeChange: setSelectedRecordType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n                lineNumber: 398,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\ImprovedDnsManager.jsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(ImprovedDnsManager, \"TYCtDhHyaCEqeegSAg1NZqzqR7c=\");\n_c = ImprovedDnsManager;\nvar _c;\n$RefreshReg$(_c, \"ImprovedDnsManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2RvbWFpbnMvSW1wcm92ZWREbnNNYW5hZ2VyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUM0QztBQVl0QjtBQUN3QjtBQUNGO0FBQ21CO0FBQ3hCO0FBRXhCLFNBQVNnQixtQkFBbUIsS0FBb0I7UUFBcEIsRUFBRUMsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBcEI7O0lBQ3pDLE1BQU0sQ0FBQ0Msa0JBQWtCQyxvQkFBb0IsR0FBR3BCLCtDQUFRQSxDQUN0RGlCLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUUksWUFBWSxLQUFJO0lBRTFCLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUd2QiwrQ0FBUUEsQ0FBQyxFQUFFO0lBQ3JELE1BQU0sQ0FBQ3dCLFNBQVNDLFdBQVcsR0FBR3pCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQzBCLG1CQUFtQkMscUJBQXFCLEdBQUczQiwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUM0QixvQkFBb0JDLHNCQUFzQixHQUFHN0IsK0NBQVFBLENBQUM7SUFFN0QsTUFBTSxDQUFDOEIsV0FBV0MsYUFBYSxHQUFHL0IsK0NBQVFBLENBQUM7SUFFM0MsaUNBQWlDO0lBQ2pDLE1BQU1nQyxjQUFjO1FBQ2xCO1lBQUVDLE1BQU07WUFBS0MsT0FBTztZQUFLQyxNQUFNakMsNEpBQUtBO1lBQUVrQyxhQUFhO1FBQWlCO1FBQ3BFO1lBQUVILE1BQU07WUFBUUMsT0FBTztZQUFRQyxNQUFNakMsNEpBQUtBO1lBQUVrQyxhQUFhO1FBQWlCO1FBQzFFO1lBQ0VILE1BQU07WUFDTkMsT0FBTztZQUNQQyxNQUFNaEMsNEpBQUlBO1lBQ1ZpQyxhQUFhO1FBQ2Y7UUFDQTtZQUFFSCxNQUFNO1lBQU1DLE9BQU87WUFBTUMsTUFBTS9CLDRKQUFJQTtZQUFFZ0MsYUFBYTtRQUFlO1FBQ25FO1lBQUVILE1BQU07WUFBT0MsT0FBTztZQUFPQyxNQUFNOUIsNEpBQVFBO1lBQUUrQixhQUFhO1FBQWU7UUFDekU7WUFBRUgsTUFBTTtZQUFNQyxPQUFPO1lBQU1DLE1BQU03Qiw2SkFBTUE7WUFBRThCLGFBQWE7UUFBZTtRQUNyRTtZQUNFSCxNQUFNO1lBQ05DLE9BQU87WUFDUEMsTUFBTTVCLDZKQUFRQTtZQUNkNkIsYUFBYTtRQUNmO0tBQ0Q7SUFFRCxtQkFBbUI7SUFDbkIsTUFBTUMsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRlosV0FBVztZQUNYYSxRQUFRQyxHQUFHLENBQ1QsdURBQXNDdEIsbUJBQUFBLDZCQUFBQSxPQUFRdUIsSUFBSSxFQUFDLFVBQW1CLE9BQVh2QixtQkFBQUEsNkJBQUFBLE9BQVF3QixFQUFFLEVBQUM7WUFHeEUsTUFBTUMsV0FBVyxNQUFNNUIsc0VBQWdCQSxDQUFDNkIsYUFBYSxDQUFDMUIsT0FBT3dCLEVBQUU7WUFDL0RILFFBQVFDLEdBQUcsQ0FBRSxzQ0FBMkJHLFNBQVNFLElBQUk7WUFFckQsSUFBSUYsU0FBU0UsSUFBSSxDQUFDQyxPQUFPLEVBQUU7Z0JBQ3pCLE1BQU1DLFVBQVVKLFNBQVNFLElBQUksQ0FBQ0UsT0FBTyxJQUFJLEVBQUU7Z0JBQzNDdkIsaUJBQWlCdUI7Z0JBQ2pCUixRQUFRQyxHQUFHLENBQUMsWUFBMkIsT0FBZk8sUUFBUUMsTUFBTSxFQUFDO2dCQUN2Qyw0RUFBNEU7Z0JBQzVFLDZGQUE2RjtnQkFFN0Ysb0NBQW9DO2dCQUNwQ2YsWUFBWWdCLE9BQU8sQ0FBQzt3QkFBQyxFQUFFZixJQUFJLEVBQUU7b0JBQzNCLE1BQU1nQixjQUFjSCxRQUFRSSxNQUFNLENBQUMsQ0FBQ0MsSUFBTUEsRUFBRWxCLElBQUksS0FBS0E7b0JBQ3JESyxRQUFRQyxHQUFHLENBQ1QsZ0JBQXVCVSxPQUFqQmhCLE1BQUssY0FBK0IsT0FBbkJnQixZQUFZRixNQUFNLEVBQUMsT0FDMUNFO2dCQUVKO1lBQ0YsT0FBTztnQkFDTCxNQUFNLElBQUlHLE1BQU1WLFNBQVNFLElBQUksQ0FBQ1MsS0FBSyxJQUFJO1lBQ3pDO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO2dCQUlGQSxpQkFDRkE7WUFKVmYsUUFBUWUsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUNmLFFBQVFlLEtBQUssQ0FBQyxvQkFBb0I7Z0JBQ2hDQyxTQUFTRCxNQUFNQyxPQUFPO2dCQUN0QlosUUFBUSxHQUFFVyxrQkFBQUEsTUFBTVgsUUFBUSxjQUFkVyxzQ0FBQUEsZ0JBQWdCVCxJQUFJO2dCQUM5QlcsTUFBTSxHQUFFRixtQkFBQUEsTUFBTVgsUUFBUSxjQUFkVyx1Q0FBQUEsaUJBQWdCRSxNQUFNO1lBQ2hDO1lBRUEsNkVBQTZFO1lBQzdFLHlFQUF5RTtZQUN6RXhDLGlEQUFLQSxDQUFDc0MsS0FBSyxDQUNUO1lBRUY5QixpQkFBaUIsRUFBRTtRQUNyQixTQUFVO1lBQ1JFLFdBQVc7UUFDYjtJQUNGO0lBRUEsdUJBQXVCO0lBQ3ZCLE1BQU0rQixxQkFBcUI7UUFDekIsSUFBSTtZQUNGN0IscUJBQXFCO1lBQ3JCVyxRQUFRQyxHQUFHLENBQUMsbURBQXNELE9BQWJ0QixtQkFBQUEsNkJBQUFBLE9BQVF1QixJQUFJO1lBQ2pFRixRQUFRQyxHQUFHLENBQUUsK0JBQW9CdEI7WUFFakMscUVBQXFFO1lBQ3JFLE1BQU13QyxVQUFVeEMsT0FBT3lDLE9BQU8sSUFBSXpDLE9BQU8wQyxhQUFhLElBQUkxQyxPQUFPd0IsRUFBRTtZQUNuRUgsUUFBUUMsR0FBRyxDQUFDLGdDQUE4QixPQUFSa0I7WUFFbEMsTUFBTWYsV0FBVyxNQUFNNUIsc0VBQWdCQSxDQUFDMEMsa0JBQWtCLENBQUNDO1lBRTNEbkIsUUFBUUMsR0FBRyxDQUFFLHlDQUE4QkcsU0FBU0UsSUFBSTtZQUV4RCxJQUFJRixTQUFTRSxJQUFJLENBQUNDLE9BQU8sRUFBRTtnQkFDekJ6QixvQkFBb0I7Z0JBQ3BCTCxpREFBS0EsQ0FBQzhCLE9BQU8sQ0FBQztnQkFFZCxzREFBc0Q7Z0JBQ3RELElBQUkzQixVQUFVO29CQUNaQSxTQUFTO3dCQUNQRyxjQUFjO3dCQUNkdUMsZ0JBQWdCLElBQUlDLE9BQU9DLFdBQVc7d0JBQ3RDQyxXQUFXckIsU0FBU0UsSUFBSSxDQUFDb0IsTUFBTSxJQUFJO29CQUNyQztnQkFDRjtnQkFFQSxNQUFNM0Isa0JBQWtCLGdDQUFnQztZQUMxRCxPQUFPO2dCQUNMLE1BQU0sSUFBSWUsTUFDUlYsU0FBU0UsSUFBSSxDQUFDUyxLQUFLLElBQUk7WUFFM0I7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZGYsUUFBUWUsS0FBSyxDQUFDLG1DQUFtQ0E7WUFDakR0QyxpREFBS0EsQ0FBQ3NDLEtBQUssQ0FBQztRQUNkLFNBQVU7WUFDUjFCLHFCQUFxQjtRQUN2QjtJQUNGO0lBSUEsaUJBQWlCO0lBQ2pCLE1BQU1zQyxrQkFBa0IsT0FBT0M7UUFDN0IsSUFBSTtZQUNGNUIsUUFBUUMsR0FBRyxDQUFFLHdCQUF1QjJCO1lBRXBDLE1BQU14QixXQUFXLE1BQU01QixzRUFBZ0JBLENBQUNxRCxZQUFZLENBQ2xEbEQsT0FBT3dCLEVBQUUsRUFDVHlCO1lBRUY1QixRQUFRQyxHQUFHLENBQUUscUNBQTBCRyxTQUFTRSxJQUFJO1lBRXBELElBQUlGLFNBQVNFLElBQUksQ0FBQ0MsT0FBTyxFQUFFO2dCQUN6QjlCLGlEQUFLQSxDQUFDOEIsT0FBTyxDQUFDLEdBQW1CLE9BQWhCcUIsV0FBV2pDLElBQUksRUFBQztnQkFDakMsTUFBTUksa0JBQWtCLGlCQUFpQjtZQUMzQyxPQUFPO2dCQUNMLE1BQU0sSUFBSWUsTUFBTVYsU0FBU0UsSUFBSSxDQUFDUyxLQUFLLElBQUk7WUFDekM7UUFDRixFQUFFLE9BQU9BLE9BQU87WUFDZGYsUUFBUWUsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUN0QyxpREFBS0EsQ0FBQ3NDLEtBQUssQ0FBQztRQUNkO0lBQ0Y7SUFFQSxrQkFBa0I7SUFDbEIsTUFBTWUsbUJBQW1CLE9BQU9GO1FBQzlCLElBQUk7WUFDRjVCLFFBQVFDLEdBQUcsQ0FBRSwwQkFBeUIyQjtZQUV0QyxNQUFNeEIsV0FBVyxNQUFNNUIsc0VBQWdCQSxDQUFDdUQsZUFBZSxDQUNyRHBELE9BQU93QixFQUFFLEVBQ1R5QixXQUFXekIsRUFBRSxFQUNieUI7WUFHRjVCLFFBQVFDLEdBQUcsQ0FBRSxzQ0FBMkJHLFNBQVNFLElBQUk7WUFFckQsSUFBSUYsU0FBU0UsSUFBSSxDQUFDQyxPQUFPLEVBQUU7Z0JBQ3pCOUIsaURBQUtBLENBQUM4QixPQUFPLENBQUMsR0FBbUIsT0FBaEJxQixXQUFXakMsSUFBSSxFQUFDO2dCQUNqQyxNQUFNSSxrQkFBa0IsaUJBQWlCO1lBQzNDLE9BQU87Z0JBQ0wsTUFBTSxJQUFJZSxNQUFNVixTQUFTRSxJQUFJLENBQUNTLEtBQUssSUFBSTtZQUN6QztRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkZixRQUFRZSxLQUFLLENBQUMsZ0NBQWdDQTtZQUM5Q3RDLGlEQUFLQSxDQUFDc0MsS0FBSyxDQUFDO1FBQ2Q7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNaUIscUJBQXFCLGVBQU9DO1lBQVVDLDBFQUFTO1FBQ25ELElBQUk7WUFDRmxDLFFBQVFDLEdBQUcsQ0FBQyx5Q0FBd0MsT0FBVGdDLFdBQVlDO1lBRXZELE1BQU05QixXQUFXLE1BQU01QixzRUFBZ0JBLENBQUMyRCxlQUFlLENBQ3JEeEQsT0FBT3dCLEVBQUUsRUFDVDhCLFVBQ0FDO1lBRUZsQyxRQUFRQyxHQUFHLENBQUUsd0NBQTZCRyxTQUFTRSxJQUFJO1lBRXZELElBQUlGLFNBQVNFLElBQUksQ0FBQ0MsT0FBTyxFQUFFO2dCQUN6QjlCLGlEQUFLQSxDQUFDOEIsT0FBTyxDQUFDO2dCQUNkLE1BQU1SLGtCQUFrQixpQkFBaUI7WUFDM0MsT0FBTztnQkFDTCxNQUFNLElBQUllLE1BQU1WLFNBQVNFLElBQUksQ0FBQ1MsS0FBSyxJQUFJO1lBQ3pDO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO1lBQ2RmLFFBQVFlLEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDdEMsaURBQUtBLENBQUNzQyxLQUFLLENBQUM7UUFDZDtJQUNGO0lBRUEsMkRBQTJEO0lBQzNELE1BQU1xQixvQkFBb0IsQ0FBQ3pDO1FBQ3pCLE9BQU9YLGNBQWM0QixNQUFNLENBQ3pCLENBQUNzQixTQUNDQSxPQUFPdkMsSUFBSSxLQUFLQSxRQUFRdUMsT0FBT0csT0FBTyxJQUFJSCxPQUFPRyxPQUFPLENBQUNDLElBQUksT0FBTztJQUUxRTtJQUlBLGdEQUFnRDtJQUNoRDNFLGdEQUFTQSxDQUFDO1FBQ1JtQixvQkFBb0JILENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUUksWUFBWSxLQUFJO0lBQzlDLEdBQUc7UUFBQ0osbUJBQUFBLDZCQUFBQSxPQUFRSSxZQUFZO0tBQUM7SUFFekJwQixnREFBU0EsQ0FBQztRQUNSLElBQUlnQixtQkFBQUEsNkJBQUFBLE9BQVF3QixFQUFFLEVBQUU7WUFDZEo7UUFDRjtJQUNGLEdBQUc7UUFBQ3BCLG1CQUFBQSw2QkFBQUEsT0FBUXdCLEVBQUU7S0FBQztJQUVmLElBQUksQ0FBQ3hCLFFBQVE7UUFDWCxxQkFDRSw4REFBQzREO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDdEUsNkpBQVdBO29CQUFDc0UsV0FBVTs7Ozs7OzhCQUN2Qiw4REFBQ0M7b0JBQUVELFdBQVU7OEJBQWlCOzs7Ozs7Ozs7Ozs7SUFHcEM7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzs4Q0FDQyw4REFBQ0c7b0NBQUdGLFdBQVU7OENBQXVDOzs7Ozs7OENBQ3JELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFFRCxXQUFVO3NEQUFnQjs7Ozs7O3NEQUM3Qiw4REFBQ0c7NENBQUtILFdBQVU7c0RBQ2I3RCxPQUFPdUIsSUFBSTs7Ozs7O3NEQUVkLDhEQUFDcUM7NENBQUlDLFdBQVU7c0RBQ1ozRCxpQ0FDQzs7a0VBQ0UsOERBQUNULDZKQUFXQTt3REFBQ29FLFdBQVU7Ozs7OztrRUFDdkIsOERBQUNHO3dEQUFLSCxXQUFVO2tFQUFxQzs7Ozs7Ozs2RUFHdkQ7O2tFQUNFLDhEQUFDbkUsNkpBQUtBO3dEQUFDbUUsV0FBVTs7Ozs7O2tFQUNqQiw4REFBQ0c7d0RBQUtILFdBQVU7a0VBQXFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU0vRCw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNJO2dDQUNDQyxTQUFTOUM7Z0NBQ1QrQyxVQUFVNUQ7Z0NBQ1ZzRCxXQUFVOztrREFFViw4REFBQ3JFLDZKQUFTQTt3Q0FBQ3FFLFdBQVcsZ0JBQThDLE9BQTlCdEQsVUFBVSxpQkFBaUI7Ozs7OztvQ0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFTaEYsQ0FBQ0wsa0NBQ0EsOERBQUMwRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUN0RSw2SkFBV0E7b0NBQUNzRSxXQUFVOzs7Ozs7OENBQ3ZCLDhEQUFDRDs7c0RBQ0MsOERBQUNROzRDQUFHUCxXQUFVO3NEQUEwQzs7Ozs7O3NEQUd4RCw4REFBQ0M7NENBQUVELFdBQVU7c0RBQXlDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTFELDhEQUFDSTs0QkFDQ0MsU0FBUzNCOzRCQUNUNEIsVUFBVTFEOzRCQUNWb0QsV0FBVTs7Z0NBRVRwRCxtQ0FDQyw4REFBQzREO29DQUFJUixXQUFVO29DQUE2Q1MsT0FBTTtvQ0FBNkJDLE1BQUs7b0NBQU9DLFNBQVE7O3NEQUNqSCw4REFBQ0M7NENBQU9aLFdBQVU7NENBQWFhLElBQUc7NENBQUtDLElBQUc7NENBQUt6QyxHQUFFOzRDQUFLMEMsUUFBTzs0Q0FBZUMsYUFBWTs7Ozs7O3NEQUN4Riw4REFBQ0M7NENBQUtqQixXQUFVOzRDQUFhVSxNQUFLOzRDQUFlUSxHQUFFOzs7Ozs7Ozs7Ozs7Z0NBRXJEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUVYsOERBQUNuQjtnQkFBSUMsV0FBVTs7b0JBRVosQ0FBQzNELGtDQUNBLDhEQUFDMEQ7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDdEUsNkpBQVdBOzRDQUFDc0UsV0FBVTs7Ozs7Ozs7Ozs7a0RBRXpCLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNPO2dEQUFHUCxXQUFVOzBEQUF5Qzs7Ozs7OzBEQUd2RCw4REFBQ0M7Z0RBQUVELFdBQVU7MERBQWdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVd0RDNELGtDQUNDLDhEQUFDMEQ7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ21CO29DQUFJbkIsV0FBVTtvQ0FBc0JvQixjQUFXOzhDQUM3Q2xFLFlBQVltRSxHQUFHLENBQUM7NENBQUMsRUFBRWxFLElBQUksRUFBRUMsS0FBSyxFQUFFO3dDQUMvQixNQUFNa0UsUUFBUTFCLGtCQUFrQnpDLE1BQU1jLE1BQU07d0NBQzVDLE1BQU1zRCxXQUFXdkUsY0FBY0c7d0NBQy9CLHFCQUNFLDhEQUFDaUQ7NENBRUNDLFNBQVMsSUFBTXBELGFBQWFFOzRDQUM1QjZDLFdBQVcsR0FJVixPQUhDdUIsV0FDSSxrQ0FDQSw4RUFDTDs7OERBRUQsOERBQUNwQjs4REFBTS9DOzs7Ozs7Z0RBQ05rRSxRQUFRLG1CQUNQLDhEQUFDbkI7b0RBQUtILFdBQVcsMkVBRWhCLE9BREN1QixXQUFXLDhCQUE4Qjs4REFFeENEOzs7Ozs7OzJDQWJBbkU7Ozs7O29DQWtCWDs7Ozs7Ozs7Ozs7MENBS0osOERBQUM0QztnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ2xFLHVEQUFjQTtvQ0FFYmtDLFNBQVM0QixrQkFBa0I1QztvQ0FDM0J3RSxZQUFZeEU7b0NBQ1p5RSxRQUFRbkM7b0NBQ1JvQyxVQUFVbEM7b0NBQ1ZtQyxPQUFPeEM7b0NBQ1BoRCxRQUFRQTtvQ0FDUk8sU0FBU0E7bUNBUEpNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBaUJkWCxvQkFBb0J1RiwrQkFDbkIsOERBQUM3RixzREFBYUE7Z0JBQ1o4RixRQUFRLENBQUMsQ0FBQ0Q7Z0JBQ1ZFLFNBQVM7b0JBQ1BDLGlCQUFpQjtvQkFDakJoRixzQkFBc0I7Z0JBQ3hCO2dCQUNBaUYsVUFBVTFDO2dCQUNWMkMsYUFBYUw7Z0JBQ2J6RixRQUFRQTtnQkFDUitGLGNBQWNwRjtnQkFDZHFGLGNBQWNwRjs7Ozs7Ozs7Ozs7O0FBS3hCO0dBell3QmI7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvZG9tYWlucy9JbXByb3ZlZERuc01hbmFnZXIuanN4P2I1NjIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQge1xuICBHbG9iZSxcbiAgTGluayxcbiAgTWFpbCxcbiAgRmlsZVRleHQsXG4gIFNlcnZlcixcbiAgU2V0dGluZ3MsXG4gIEFsZXJ0Q2lyY2xlLFxuICBSZWZyZXNoQ3csXG4gIENoZWNrQ2lyY2xlLFxuICBDbG9jayxcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IERuc1JlY29yZFRhYmxlIGZyb20gXCIuL0Ruc1JlY29yZFRhYmxlXCI7XG5pbXBvcnQgRG5zUmVjb3JkRm9ybSBmcm9tIFwiLi9EbnNSZWNvcmRGb3JtXCI7XG5pbXBvcnQgZG9tYWluTW5nU2VydmljZSBmcm9tIFwiQC9hcHAvc2VydmljZXMvZG9tYWluTW5nU2VydmljZVwiO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tIFwicmVhY3QtdG9hc3RpZnlcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSW1wcm92ZWREbnNNYW5hZ2VyKHsgZG9tYWluLCBvblVwZGF0ZSB9KSB7XG4gIGNvbnN0IFtkbnNTZXJ2aWNlQWN0aXZlLCBzZXREbnNTZXJ2aWNlQWN0aXZlXSA9IHVzZVN0YXRlKFxuICAgIGRvbWFpbj8uZG5zQWN0aXZhdGVkIHx8IGZhbHNlXG4gICk7XG4gIGNvbnN0IFthbGxEbnNSZWNvcmRzLCBzZXRBbGxEbnNSZWNvcmRzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbYWN0aXZhdGluZ1NlcnZpY2UsIHNldEFjdGl2YXRpbmdTZXJ2aWNlXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3NlbGVjdGVkUmVjb3JkVHlwZSwgc2V0U2VsZWN0ZWRSZWNvcmRUeXBlXSA9IHVzZVN0YXRlKFwiQVwiKTtcblxuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoXCJBXCIpO1xuXG4gIC8vIEROUyByZWNvcmQgdHlwZXMgY29uZmlndXJhdGlvblxuICBjb25zdCByZWNvcmRUeXBlcyA9IFtcbiAgICB7IHR5cGU6IFwiQVwiLCBsYWJlbDogXCJBXCIsIGljb246IEdsb2JlLCBkZXNjcmlwdGlvbjogXCJJUHY0IGFkZHJlc3Nlc1wiIH0sXG4gICAgeyB0eXBlOiBcIkFBQUFcIiwgbGFiZWw6IFwiQUFBQVwiLCBpY29uOiBHbG9iZSwgZGVzY3JpcHRpb246IFwiSVB2NiBhZGRyZXNzZXNcIiB9LFxuICAgIHtcbiAgICAgIHR5cGU6IFwiQ05BTUVcIixcbiAgICAgIGxhYmVsOiBcIkNOQU1FXCIsXG4gICAgICBpY29uOiBMaW5rLFxuICAgICAgZGVzY3JpcHRpb246IFwiQ2Fub25pY2FsIG5hbWVzXCIsXG4gICAgfSxcbiAgICB7IHR5cGU6IFwiTVhcIiwgbGFiZWw6IFwiTVhcIiwgaWNvbjogTWFpbCwgZGVzY3JpcHRpb246IFwiTWFpbCBzZXJ2ZXJzXCIgfSxcbiAgICB7IHR5cGU6IFwiVFhUXCIsIGxhYmVsOiBcIlRYVFwiLCBpY29uOiBGaWxlVGV4dCwgZGVzY3JpcHRpb246IFwiVGV4dCByZWNvcmRzXCIgfSxcbiAgICB7IHR5cGU6IFwiTlNcIiwgbGFiZWw6IFwiTlNcIiwgaWNvbjogU2VydmVyLCBkZXNjcmlwdGlvbjogXCJOYW1lIHNlcnZlcnNcIiB9LFxuICAgIHtcbiAgICAgIHR5cGU6IFwiU1JWXCIsXG4gICAgICBsYWJlbDogXCJTUlZcIixcbiAgICAgIGljb246IFNldHRpbmdzLFxuICAgICAgZGVzY3JpcHRpb246IFwiU2VydmljZSByZWNvcmRzXCIsXG4gICAgfSxcbiAgXTtcblxuICAvLyBMb2FkIEROUyByZWNvcmRzXG4gIGNvbnN0IGxvYWREbnNSZWNvcmRzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coXG4gICAgICAgIGDwn5SNIExvYWRpbmcgRE5TIHJlY29yZHMgZm9yIGRvbWFpbjogJHtkb21haW4/Lm5hbWV9IChJRDogJHtkb21haW4/LmlkfSlgXG4gICAgICApO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRvbWFpbk1uZ1NlcnZpY2UuZ2V0RG5zUmVjb3Jkcyhkb21haW4uaWQpO1xuICAgICAgY29uc29sZS5sb2coYPCfk4sgRE5TIFJlY29yZHMgUmVzcG9uc2U6YCwgcmVzcG9uc2UuZGF0YSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgY29uc3QgcmVjb3JkcyA9IHJlc3BvbnNlLmRhdGEucmVjb3JkcyB8fCBbXTtcbiAgICAgICAgc2V0QWxsRG5zUmVjb3JkcyhyZWNvcmRzKTtcbiAgICAgICAgY29uc29sZS5sb2coYOKchSBMb2FkZWQgJHtyZWNvcmRzLmxlbmd0aH0gRE5TIHJlY29yZHNgKTtcbiAgICAgICAgLy8gRG9uJ3QgYXV0b21hdGljYWxseSBzZXQgRE5TIHNlcnZpY2UgYXMgYWN0aXZlIGp1c3QgYmVjYXVzZSByZWNvcmRzIGxvYWRlZFxuICAgICAgICAvLyBUaGUgYWN0aXZhdGlvbiBzdGF0dXMgc2hvdWxkIGNvbWUgZnJvbSB0aGUgZG9tYWluIGRhdGEsIG5vdCBmcm9tIHN1Y2Nlc3NmdWwgcmVjb3JkIGxvYWRpbmdcblxuICAgICAgICAvLyBMb2cgcmVjb3JkcyBieSB0eXBlIGZvciBkZWJ1Z2dpbmdcbiAgICAgICAgcmVjb3JkVHlwZXMuZm9yRWFjaCgoeyB0eXBlIH0pID0+IHtcbiAgICAgICAgICBjb25zdCB0eXBlUmVjb3JkcyA9IHJlY29yZHMuZmlsdGVyKChyKSA9PiByLnR5cGUgPT09IHR5cGUpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICAgICAgYPCfk4ogJHt0eXBlfSBSZWNvcmRzICgke3R5cGVSZWNvcmRzLmxlbmd0aH0pOmAsXG4gICAgICAgICAgICB0eXBlUmVjb3Jkc1xuICAgICAgICAgICk7XG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLmRhdGEuZXJyb3IgfHwgXCJGYWlsZWQgdG8gbG9hZCBETlMgcmVjb3Jkc1wiKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIuKdjCBFcnJvciBsb2FkaW5nIEROUyByZWNvcmRzOlwiLCBlcnJvcik7XG4gICAgICBjb25zb2xlLmVycm9yKFwi4p2MIEVycm9yIGRldGFpbHM6XCIsIHtcbiAgICAgICAgbWVzc2FnZTogZXJyb3IubWVzc2FnZSxcbiAgICAgICAgcmVzcG9uc2U6IGVycm9yLnJlc3BvbnNlPy5kYXRhLFxuICAgICAgICBzdGF0dXM6IGVycm9yLnJlc3BvbnNlPy5zdGF0dXMsXG4gICAgICB9KTtcblxuICAgICAgLy8gRG9uJ3QgY2hhbmdlIEROUyBzZXJ2aWNlIGFjdGl2YXRpb24gc3RhdHVzIGJhc2VkIG9uIHJlY29yZCBsb2FkaW5nIGZhaWx1cmVcbiAgICAgIC8vIFRoZSBhY3RpdmF0aW9uIHN0YXR1cyBzaG91bGQgb25seSBiZSBkZXRlcm1pbmVkIGJ5IGRvbWFpbi5kbnNBY3RpdmF0ZWRcbiAgICAgIHRvYXN0LmVycm9yKFxuICAgICAgICBcIkZhaWxlZCB0byBsb2FkIEROUyByZWNvcmRzLiBQbGVhc2UgY2hlY2sgaWYgRE5TIHNlcnZpY2UgaXMgYWN0aXZhdGVkLlwiXG4gICAgICApO1xuICAgICAgc2V0QWxsRG5zUmVjb3JkcyhbXSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBBY3RpdmF0ZSBETlMgc2VydmljZVxuICBjb25zdCBhY3RpdmF0ZURuc1NlcnZpY2UgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEFjdGl2YXRpbmdTZXJ2aWNlKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coYPCfmoAgQWN0aXZhdGluZyBETlMgc2VydmljZSBmb3IgZG9tYWluOiAke2RvbWFpbj8ubmFtZX1gKTtcbiAgICAgIGNvbnNvbGUubG9nKGDwn5SNIERvbWFpbiBvYmplY3Q6YCwgZG9tYWluKTtcblxuICAgICAgLy8gVGhlIGJhY2tlbmQgZXhwZWN0cyBvcmRlcklkLCB3aGljaCBzaG91bGQgYmUgdGhlIGRvbWFpbidzIG9yZGVyIElEXG4gICAgICBjb25zdCBvcmRlcklkID0gZG9tYWluLm9yZGVyaWQgfHwgZG9tYWluLmRvbWFpbk9yZGVySWQgfHwgZG9tYWluLmlkO1xuICAgICAgY29uc29sZS5sb2coYPCfk4sgVXNpbmcgb3JkZXIgSUQ6ICR7b3JkZXJJZH1gKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkb21haW5NbmdTZXJ2aWNlLmFjdGl2YXRlRG5zU2VydmljZShvcmRlcklkKTtcblxuICAgICAgY29uc29sZS5sb2coYPCfk4sgRE5TIEFjdGl2YXRpb24gUmVzcG9uc2U6YCwgcmVzcG9uc2UuZGF0YSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0RG5zU2VydmljZUFjdGl2ZSh0cnVlKTtcbiAgICAgICAgdG9hc3Quc3VjY2VzcyhcIkROUyBzZXJ2aWNlIGFjdGl2YXRlZCBzdWNjZXNzZnVsbHkhXCIpO1xuXG4gICAgICAgIC8vIFVwZGF0ZSB0aGUgZG9tYWluIG9iamVjdCB3aXRoIEROUyBhY3RpdmF0aW9uIHN0YXR1c1xuICAgICAgICBpZiAob25VcGRhdGUpIHtcbiAgICAgICAgICBvblVwZGF0ZSh7XG4gICAgICAgICAgICBkbnNBY3RpdmF0ZWQ6IHRydWUsXG4gICAgICAgICAgICBkbnNBY3RpdmF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgZG5zWm9uZUlkOiByZXNwb25zZS5kYXRhLnpvbmVJZCB8fCBudWxsLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9XG5cbiAgICAgICAgYXdhaXQgbG9hZERuc1JlY29yZHMoKTsgLy8gTG9hZCByZWNvcmRzIGFmdGVyIGFjdGl2YXRpb25cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgICByZXNwb25zZS5kYXRhLmVycm9yIHx8IFwiRmFpbGVkIHRvIGFjdGl2YXRlIEROUyBzZXJ2aWNlXCJcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIuKdjCBFcnJvciBhY3RpdmF0aW5nIEROUyBzZXJ2aWNlOlwiLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byBhY3RpdmF0ZSBETlMgc2VydmljZVwiKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0QWN0aXZhdGluZ1NlcnZpY2UoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuXG5cbiAgLy8gQWRkIEROUyByZWNvcmRcbiAgY29uc3QgaGFuZGxlQWRkUmVjb3JkID0gYXN5bmMgKHJlY29yZERhdGEpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coYOKelSBBZGRpbmcgRE5TIHJlY29yZDpgLCByZWNvcmREYXRhKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkb21haW5NbmdTZXJ2aWNlLmFkZERuc1JlY29yZChcbiAgICAgICAgZG9tYWluLmlkLFxuICAgICAgICByZWNvcmREYXRhXG4gICAgICApO1xuICAgICAgY29uc29sZS5sb2coYPCfk4sgQWRkIFJlY29yZCBSZXNwb25zZTpgLCByZXNwb25zZS5kYXRhKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuc3VjY2Vzcykge1xuICAgICAgICB0b2FzdC5zdWNjZXNzKGAke3JlY29yZERhdGEudHlwZX0gcmVjb3JkIGFkZGVkIHN1Y2Nlc3NmdWxseSFgKTtcbiAgICAgICAgYXdhaXQgbG9hZERuc1JlY29yZHMoKTsgLy8gUmVsb2FkIHJlY29yZHNcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5kYXRhLmVycm9yIHx8IFwiRmFpbGVkIHRvIGFkZCBETlMgcmVjb3JkXCIpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKFwi4p2MIEVycm9yIGFkZGluZyBETlMgcmVjb3JkOlwiLCBlcnJvcik7XG4gICAgICB0b2FzdC5lcnJvcihcIkZhaWxlZCB0byBhZGQgRE5TIHJlY29yZFwiKTtcbiAgICB9XG4gIH07XG5cbiAgLy8gRWRpdCBETlMgcmVjb3JkXG4gIGNvbnN0IGhhbmRsZUVkaXRSZWNvcmQgPSBhc3luYyAocmVjb3JkRGF0YSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZyhg4pyP77iPIEVkaXRpbmcgRE5TIHJlY29yZDpgLCByZWNvcmREYXRhKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBkb21haW5NbmdTZXJ2aWNlLnVwZGF0ZURuc1JlY29yZChcbiAgICAgICAgZG9tYWluLmlkLFxuICAgICAgICByZWNvcmREYXRhLmlkLFxuICAgICAgICByZWNvcmREYXRhXG4gICAgICApO1xuXG4gICAgICBjb25zb2xlLmxvZyhg8J+TiyBFZGl0IFJlY29yZCBSZXNwb25zZTpgLCByZXNwb25zZS5kYXRhKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuc3VjY2Vzcykge1xuICAgICAgICB0b2FzdC5zdWNjZXNzKGAke3JlY29yZERhdGEudHlwZX0gcmVjb3JkIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5IWApO1xuICAgICAgICBhd2FpdCBsb2FkRG5zUmVjb3JkcygpOyAvLyBSZWxvYWQgcmVjb3Jkc1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLmRhdGEuZXJyb3IgfHwgXCJGYWlsZWQgdG8gdXBkYXRlIEROUyByZWNvcmRcIik7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCLinYwgRXJyb3IgdXBkYXRpbmcgRE5TIHJlY29yZDpcIiwgZXJyb3IpO1xuICAgICAgdG9hc3QuZXJyb3IoXCJGYWlsZWQgdG8gdXBkYXRlIEROUyByZWNvcmRcIik7XG4gICAgfVxuICB9O1xuXG4gIC8vIERlbGV0ZSBETlMgcmVjb3JkXG4gIGNvbnN0IGhhbmRsZURlbGV0ZVJlY29yZCA9IGFzeW5jIChyZWNvcmRJZCwgcmVjb3JkID0gbnVsbCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZyhg8J+Xke+4jyBEZWxldGluZyBETlMgcmVjb3JkIElEOiAke3JlY29yZElkfWAsIHJlY29yZCk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZG9tYWluTW5nU2VydmljZS5kZWxldGVEbnNSZWNvcmQoXG4gICAgICAgIGRvbWFpbi5pZCxcbiAgICAgICAgcmVjb3JkSWQsXG4gICAgICAgIHJlY29yZFxuICAgICAgKTtcbiAgICAgIGNvbnNvbGUubG9nKGDwn5OLIERlbGV0ZSBSZWNvcmQgUmVzcG9uc2U6YCwgcmVzcG9uc2UuZGF0YSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgdG9hc3Quc3VjY2VzcyhcIkROUyByZWNvcmQgZGVsZXRlZCBzdWNjZXNzZnVsbHkhXCIpO1xuICAgICAgICBhd2FpdCBsb2FkRG5zUmVjb3JkcygpOyAvLyBSZWxvYWQgcmVjb3Jkc1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3BvbnNlLmRhdGEuZXJyb3IgfHwgXCJGYWlsZWQgdG8gZGVsZXRlIEROUyByZWNvcmRcIik7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCLinYwgRXJyb3IgZGVsZXRpbmcgRE5TIHJlY29yZDpcIiwgZXJyb3IpO1xuICAgICAgdG9hc3QuZXJyb3IoXCJGYWlsZWQgdG8gZGVsZXRlIEROUyByZWNvcmRcIik7XG4gICAgfVxuICB9O1xuXG4gIC8vIEdldCByZWNvcmRzIGZvciBzcGVjaWZpYyB0eXBlIChmaWx0ZXIgb3V0IGVtcHR5IHJlY29yZHMpXG4gIGNvbnN0IGdldFJlY29yZHNGb3JUeXBlID0gKHR5cGUpID0+IHtcbiAgICByZXR1cm4gYWxsRG5zUmVjb3Jkcy5maWx0ZXIoXG4gICAgICAocmVjb3JkKSA9PlxuICAgICAgICByZWNvcmQudHlwZSA9PT0gdHlwZSAmJiByZWNvcmQuY29udGVudCAmJiByZWNvcmQuY29udGVudC50cmltKCkgIT09IFwiXCJcbiAgICApO1xuICB9O1xuXG5cblxuICAvLyBVcGRhdGUgRE5TIHNlcnZpY2Ugc3RhdHVzIHdoZW4gZG9tYWluIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXREbnNTZXJ2aWNlQWN0aXZlKGRvbWFpbj8uZG5zQWN0aXZhdGVkIHx8IGZhbHNlKTtcbiAgfSwgW2RvbWFpbj8uZG5zQWN0aXZhdGVkXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoZG9tYWluPy5pZCkge1xuICAgICAgbG9hZERuc1JlY29yZHMoKTtcbiAgICB9XG4gIH0sIFtkb21haW4/LmlkXSk7XG5cbiAgaWYgKCFkb21haW4pIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1hbWJlci01MCBib3JkZXIgYm9yZGVyLWFtYmVyLTIwMCByb3VuZGVkLWxnIHAtNCBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWFtYmVyLTYwMCBmbGV4LXNocmluay0wXCIgLz5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1hbWJlci04MDBcIj5Eb21haW4gaW5mb3JtYXRpb24gaXMgcmVxdWlyZWQgdG8gbWFuYWdlIEROUyByZWNvcmRzLjwvcD5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG9cIj5cbiAgICAgIHsvKiBIZWFkZXIgU2VjdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwIHB4LTYgcHktOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+RE5TIE1hbmFnZW1lbnQ8L2gxPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMyBtdC0yXCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5NYW5hZ2UgRE5TIHJlY29yZHMgZm9yPC9wPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXNtIGZvbnQtbWVkaXVtIGJnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDBcIj5cbiAgICAgICAgICAgICAgICB7ZG9tYWluLm5hbWV9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIHtkbnNTZXJ2aWNlQWN0aXZlID8gKFxuICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgPENoZWNrQ2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tNjAwIGZvbnQtbWVkaXVtXCI+RE5TIEFjdGl2ZTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWFtYmVyLTYwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1hbWJlci02MDAgZm9udC1tZWRpdW1cIj5ETlMgSW5hY3RpdmU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17bG9hZERuc1JlY29yZHN9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBzaGFkb3ctc20gdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJnLXdoaXRlIGhvdmVyOmJnLWdyYXktNTAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT17YGgtNCB3LTQgbXItMiAke2xvYWRpbmcgPyBcImFuaW1hdGUtc3BpblwiIDogXCJcIn1gfSAvPlxuICAgICAgICAgICAgICBSZWZyZXNoXG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRE5TIFNlcnZpY2UgU3RhdHVzICovfVxuICAgICAgeyFkbnNTZXJ2aWNlQWN0aXZlICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1hbWJlci01MCBib3JkZXIgYm9yZGVyLWFtYmVyLTIwMCByb3VuZGVkLWxnIHAtNiBteC02IG10LTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgZ2FwLTNcIj5cbiAgICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1hbWJlci02MDAgbXQtMC41IGZsZXgtc2hyaW5rLTBcIiAvPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtYW1iZXItOTAwIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgIEROUyBTZXJ2aWNlIE5vdCBBY3RpdmVcbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtYW1iZXItODAwIHRleHQtc20gbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICBZb3UgbmVlZCB0byBhY3RpdmF0ZSBETlMgc2VydmljZSBiZWZvcmUgeW91IGNhbiBtYW5hZ2UgRE5TIHJlY29yZHMgZm9yIHRoaXMgZG9tYWluLlxuICAgICAgICAgICAgICAgICAgT25jZSBhY3RpdmF0ZWQsIHlvdSdsbCBiZSBhYmxlIHRvIGFkZCwgZWRpdCwgYW5kIGRlbGV0ZSBETlMgcmVjb3Jkcy5cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2FjdGl2YXRlRG5zU2VydmljZX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FjdGl2YXRpbmdTZXJ2aWNlfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgcm91bmRlZC1tZCBzaGFkb3ctc20gdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXdoaXRlIGJnLWFtYmVyLTYwMCBob3ZlcjpiZy1hbWJlci03MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLW9mZnNldC0yIGZvY3VzOnJpbmctYW1iZXItNTAwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGZsZXgtc2hyaW5rLTBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7YWN0aXZhdGluZ1NlcnZpY2UgJiYgKFxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIC1tbC0xIG1yLTIgaC00IHctNCB0ZXh0LXdoaXRlXCIgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIGZpbGw9XCJub25lXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPGNpcmNsZSBjbGFzc05hbWU9XCJvcGFjaXR5LTI1XCIgY3g9XCIxMlwiIGN5PVwiMTJcIiByPVwiMTBcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiBzdHJva2VXaWR0aD1cIjRcIj48L2NpcmNsZT5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIGNsYXNzTmFtZT1cIm9wYWNpdHktNzVcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgZD1cIk00IDEyYTggOCAwIDAxOC04VjBDNS4zNzMgMCAwIDUuMzczIDAgMTJoNHptMiA1LjI5MUE3Ljk2MiA3Ljk2MiAwIDAxNCAxMkgwYzAgMy4wNDIgMS4xMzUgNS44MjQgMyA3LjkzOGwzLTIuNjQ3elwiPjwvcGF0aD5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgQWN0aXZhdGUgRE5TXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogTWFpbiBDb250ZW50IEFyZWEgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTYgcGItNlwiPlxuICAgICAgICB7LyogTWVzc2FnZSB3aGVuIEROUyBpcyBub3QgYWN0aXZlIC0gc2hvd24gaW5zdGVhZCBvZiByZWNvcmRzIHRhYmxlICovfVxuICAgICAgICB7IWRuc1NlcnZpY2VBY3RpdmUgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCByb3VuZGVkLWxnIHNoYWRvdy1zbSBtdC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LTE2XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMjAgaC0yMCBiZy1hbWJlci0xMDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC0xMCB3LTEwIHRleHQtYW1iZXItNjAwXCIgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LW1kXCI+XG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTNcIj5cbiAgICAgICAgICAgICAgICAgICAgRE5TIE1hbmFnZW1lbnQgTm90IEF2YWlsYWJsZVxuICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbGVhZGluZy1yZWxheGVkXCI+XG4gICAgICAgICAgICAgICAgICAgIEROUyBzZXJ2aWNlIGlzIG5vdCBhY3RpdmF0ZWQgZm9yIHRoaXMgZG9tYWluLiBQbGVhc2UgYWN0aXZhdGUgRE5TIHNlcnZpY2VcbiAgICAgICAgICAgICAgICAgICAgdXNpbmcgdGhlIGJ1dHRvbiBhYm92ZSB0byBzdGFydCBtYW5hZ2luZyB5b3VyIEROUyByZWNvcmRzLlxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBETlMgUmVjb3JkcyBTZWN0aW9uIC0gT25seSBzaG93IHdoZW4gRE5TIHNlcnZpY2UgaXMgYWN0aXZlICovfVxuICAgICAgICB7ZG5zU2VydmljZUFjdGl2ZSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgc2hhZG93LXNtIG10LTZcIj5cbiAgICAgICAgICAgIHsvKiBUYWIgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTggcHgtNlwiIGFyaWEtbGFiZWw9XCJUYWJzXCI+XG4gICAgICAgICAgICAgICAge3JlY29yZFR5cGVzLm1hcCgoeyB0eXBlLCBsYWJlbCB9KSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBjb3VudCA9IGdldFJlY29yZHNGb3JUeXBlKHR5cGUpLmxlbmd0aDtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IGlzQWN0aXZlID0gYWN0aXZlVGFiID09PSB0eXBlO1xuICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIGtleT17dHlwZX1cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVUYWIodHlwZSl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlzQWN0aXZlXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JvcmRlci1ibHVlLTUwMCB0ZXh0LWJsdWUtNjAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktNzAwIGhvdmVyOmJvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICAgICAgICB9IHdoaXRlc3BhY2Utbm93cmFwIHB5LTQgcHgtMSBib3JkZXItYi0yIGZvbnQtbWVkaXVtIHRleHQtc20gZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJgfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+e2xhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICB7Y291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yLjUgcHktMC41IHJvdW5kZWQtZnVsbCB0ZXh0LXhzIGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGlzQWN0aXZlID8gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAnIDogJ2JnLWdyYXktMTAwIHRleHQtZ3JheS04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3VudH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgIDwvbmF2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBUYWIgQ29udGVudCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02XCI+XG4gICAgICAgICAgICAgIDxEbnNSZWNvcmRUYWJsZVxuICAgICAgICAgICAgICAgIGtleT17YWN0aXZlVGFifVxuICAgICAgICAgICAgICAgIHJlY29yZHM9e2dldFJlY29yZHNGb3JUeXBlKGFjdGl2ZVRhYil9XG4gICAgICAgICAgICAgICAgcmVjb3JkVHlwZT17YWN0aXZlVGFifVxuICAgICAgICAgICAgICAgIG9uRWRpdD17aGFuZGxlRWRpdFJlY29yZH1cbiAgICAgICAgICAgICAgICBvbkRlbGV0ZT17aGFuZGxlRGVsZXRlUmVjb3JkfVxuICAgICAgICAgICAgICAgIG9uQWRkPXtoYW5kbGVBZGRSZWNvcmR9XG4gICAgICAgICAgICAgICAgZG9tYWluPXtkb21haW59XG4gICAgICAgICAgICAgICAgbG9hZGluZz17bG9hZGluZ31cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cblxuXG4gICAgICB7LyogRWRpdCBSZWNvcmQgRm9ybSAtIE9ubHkgc2hvdyB3aGVuIGVkaXRpbmcgYW5kIEROUyBzZXJ2aWNlIGlzIGFjdGl2ZSAqL31cbiAgICAgIHtkbnNTZXJ2aWNlQWN0aXZlICYmIGVkaXRpbmdSZWNvcmQgJiYgKFxuICAgICAgICA8RG5zUmVjb3JkRm9ybVxuICAgICAgICAgIGlzT3Blbj17ISFlZGl0aW5nUmVjb3JkfVxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHtcbiAgICAgICAgICAgIHNldEVkaXRpbmdSZWNvcmQobnVsbCk7XG4gICAgICAgICAgICBzZXRTZWxlY3RlZFJlY29yZFR5cGUoXCJBXCIpO1xuICAgICAgICAgIH19XG4gICAgICAgICAgb25TdWJtaXQ9e2hhbmRsZUVkaXRSZWNvcmR9XG4gICAgICAgICAgaW5pdGlhbERhdGE9e2VkaXRpbmdSZWNvcmR9XG4gICAgICAgICAgZG9tYWluPXtkb21haW59XG4gICAgICAgICAgc2VsZWN0ZWRUeXBlPXtzZWxlY3RlZFJlY29yZFR5cGV9XG4gICAgICAgICAgb25UeXBlQ2hhbmdlPXtzZXRTZWxlY3RlZFJlY29yZFR5cGV9XG4gICAgICAgIC8+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiR2xvYmUiLCJMaW5rIiwiTWFpbCIsIkZpbGVUZXh0IiwiU2VydmVyIiwiU2V0dGluZ3MiLCJBbGVydENpcmNsZSIsIlJlZnJlc2hDdyIsIkNoZWNrQ2lyY2xlIiwiQ2xvY2siLCJEbnNSZWNvcmRUYWJsZSIsIkRuc1JlY29yZEZvcm0iLCJkb21haW5NbmdTZXJ2aWNlIiwidG9hc3QiLCJJbXByb3ZlZERuc01hbmFnZXIiLCJkb21haW4iLCJvblVwZGF0ZSIsImRuc1NlcnZpY2VBY3RpdmUiLCJzZXREbnNTZXJ2aWNlQWN0aXZlIiwiZG5zQWN0aXZhdGVkIiwiYWxsRG5zUmVjb3JkcyIsInNldEFsbERuc1JlY29yZHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImFjdGl2YXRpbmdTZXJ2aWNlIiwic2V0QWN0aXZhdGluZ1NlcnZpY2UiLCJzZWxlY3RlZFJlY29yZFR5cGUiLCJzZXRTZWxlY3RlZFJlY29yZFR5cGUiLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJyZWNvcmRUeXBlcyIsInR5cGUiLCJsYWJlbCIsImljb24iLCJkZXNjcmlwdGlvbiIsImxvYWREbnNSZWNvcmRzIiwiY29uc29sZSIsImxvZyIsIm5hbWUiLCJpZCIsInJlc3BvbnNlIiwiZ2V0RG5zUmVjb3JkcyIsImRhdGEiLCJzdWNjZXNzIiwicmVjb3JkcyIsImxlbmd0aCIsImZvckVhY2giLCJ0eXBlUmVjb3JkcyIsImZpbHRlciIsInIiLCJFcnJvciIsImVycm9yIiwibWVzc2FnZSIsInN0YXR1cyIsImFjdGl2YXRlRG5zU2VydmljZSIsIm9yZGVySWQiLCJvcmRlcmlkIiwiZG9tYWluT3JkZXJJZCIsImRuc0FjdGl2YXRlZEF0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiZG5zWm9uZUlkIiwiem9uZUlkIiwiaGFuZGxlQWRkUmVjb3JkIiwicmVjb3JkRGF0YSIsImFkZERuc1JlY29yZCIsImhhbmRsZUVkaXRSZWNvcmQiLCJ1cGRhdGVEbnNSZWNvcmQiLCJoYW5kbGVEZWxldGVSZWNvcmQiLCJyZWNvcmRJZCIsInJlY29yZCIsImRlbGV0ZURuc1JlY29yZCIsImdldFJlY29yZHNGb3JUeXBlIiwiY29udGVudCIsInRyaW0iLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwiaDEiLCJzcGFuIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiaDMiLCJzdmciLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94IiwiY2lyY2xlIiwiY3giLCJjeSIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwicGF0aCIsImQiLCJuYXYiLCJhcmlhLWxhYmVsIiwibWFwIiwiY291bnQiLCJpc0FjdGl2ZSIsInJlY29yZFR5cGUiLCJvbkVkaXQiLCJvbkRlbGV0ZSIsIm9uQWRkIiwiZWRpdGluZ1JlY29yZCIsImlzT3BlbiIsIm9uQ2xvc2UiLCJzZXRFZGl0aW5nUmVjb3JkIiwib25TdWJtaXQiLCJpbml0aWFsRGF0YSIsInNlbGVjdGVkVHlwZSIsIm9uVHlwZUNoYW5nZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx":
/*!***************************************************!*\
  !*** ./src/components/domains/DnsRecordTable.jsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DnsRecordTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Copy,Edit,FileText,Globe,Info,Link,Mail,Plus,Save,Server,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants/dnsRecords */ \"(app-pages-browser)/./src/constants/dnsRecords.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DnsRecordTable(param) {\n    let { records, recordType, onEdit, onDelete, onAdd, domain, loading = false } = param;\n    _s();\n    const [deleteConfirm, setDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showInlineForm, setShowInlineForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Inline editing state\n    const [editingRecordId, setEditingRecordId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editFormData, setEditFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [editFormErrors, setEditFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isEditSubmitting, setIsEditSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Reset form state when record type changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setShowInlineForm(false);\n        setFormData({});\n        setFormErrors({});\n    }, [\n        recordType\n    ]);\n    // Initialize form data for the current record type\n    const initializeFormData = (type)=>{\n        const recordTypeConfig = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DNS_RECORD_TYPES[type];\n        if (!recordTypeConfig) return {\n            type\n        };\n        const initialData = {\n            type\n        };\n        recordTypeConfig.fields.forEach((field)=>{\n            if (field.name === \"ttl\") {\n                initialData[field.name] = field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL;\n            } else if (field.type === \"number\") {\n                initialData[field.name] = field.default || 0;\n            } else {\n                initialData[field.name] = field.default || \"\";\n            }\n        });\n        return initialData;\n    };\n    // Handle add button click - shows inline form\n    const handleAddClick = ()=>{\n        const initialData = initializeFormData(recordType);\n        setFormData(initialData);\n        setFormErrors({});\n        setShowInlineForm(true);\n    };\n    // Handle form field changes\n    const handleFieldChange = (fieldName, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [fieldName]: value\n            }));\n        // Clear error for this field\n        if (formErrors[fieldName]) {\n            setFormErrors((prev)=>({\n                    ...prev,\n                    [fieldName]: null\n                }));\n        }\n    };\n    // Handle form submission\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            // Validate the form data\n            const validation = (0,_constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.validateDnsRecord)(formData);\n            if (!validation.isValid) {\n                setFormErrors(validation.errors);\n                return;\n            }\n            await onAdd(formData);\n            setShowInlineForm(false);\n            setFormData({});\n            setFormErrors({});\n        } catch (error) {\n            console.error(\"Error adding record:\", error);\n        // Handle validation errors if needed\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // Handle form cancel\n    const handleCancel = ()=>{\n        setShowInlineForm(false);\n        setFormData({});\n        setFormErrors({});\n    };\n    // Handle edit button click - shows inline edit form\n    const handleEditClick = (record)=>{\n        setEditingRecordId(record.id);\n        setEditFormData({\n            ...record,\n            originalContent: record.content // Store original content for API\n        });\n        setEditFormErrors({});\n        setShowInlineForm(false); // Hide add form if open\n    };\n    // Handle edit form field changes\n    const handleEditFieldChange = (fieldName, value)=>{\n        setEditFormData((prev)=>({\n                ...prev,\n                [fieldName]: value\n            }));\n        // Clear error for this field\n        if (editFormErrors[fieldName]) {\n            setEditFormErrors((prev)=>({\n                    ...prev,\n                    [fieldName]: undefined\n                }));\n        }\n    };\n    // Handle edit form submission\n    const handleEditSubmit = async (e)=>{\n        e.preventDefault();\n        setIsEditSubmitting(true);\n        try {\n            // Validate the form data\n            const validation = (0,_constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.validateDnsRecord)(editFormData);\n            if (!validation.isValid) {\n                setEditFormErrors(validation.errors);\n                return;\n            }\n            await onEdit(editFormData);\n            setEditingRecordId(null);\n            setEditFormData({});\n            setEditFormErrors({});\n        } catch (error) {\n            console.error(\"Error editing record:\", error);\n        // Handle validation errors if needed\n        } finally{\n            setIsEditSubmitting(false);\n        }\n    };\n    // Handle edit form cancel\n    const handleEditCancel = ()=>{\n        setEditingRecordId(null);\n        setEditFormData({});\n        setEditFormErrors({});\n    };\n    // Get record type configuration\n    const getRecordTypeConfig = (type)=>{\n        const configs = {\n            A: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                label: \"IPv4 Address\",\n                color: \"blue\",\n                bgColor: \"bg-blue-50\",\n                textColor: \"text-blue-600\",\n                borderColor: \"border-blue-200\"\n            },\n            AAAA: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                label: \"IPv6 Address\",\n                color: \"purple\",\n                bgColor: \"bg-purple-50\",\n                textColor: \"text-purple-600\",\n                borderColor: \"border-purple-200\"\n            },\n            CNAME: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                label: \"Canonical Name\",\n                color: \"green\",\n                bgColor: \"bg-green-50\",\n                textColor: \"text-green-600\",\n                borderColor: \"border-green-200\"\n            },\n            MX: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                label: \"Mail Exchange\",\n                color: \"orange\",\n                bgColor: \"bg-orange-50\",\n                textColor: \"text-orange-600\",\n                borderColor: \"border-orange-200\"\n            },\n            TXT: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                label: \"Text Record\",\n                color: \"gray\",\n                bgColor: \"bg-gray-50\",\n                textColor: \"text-gray-600\",\n                borderColor: \"border-gray-200\"\n            },\n            NS: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                label: \"Name Server\",\n                color: \"indigo\",\n                bgColor: \"bg-indigo-50\",\n                textColor: \"text-indigo-600\",\n                borderColor: \"border-indigo-200\"\n            },\n            SRV: {\n                icon: _barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                label: \"Service Record\",\n                color: \"pink\",\n                bgColor: \"bg-pink-50\",\n                textColor: \"text-pink-600\",\n                borderColor: \"border-pink-200\"\n            }\n        };\n        return configs[type] || configs.A;\n    };\n    // Format TTL display\n    const formatTTL = (ttl)=>{\n        const numericTTL = parseInt(ttl);\n        // Format TTL values in a human-readable way\n        if (numericTTL >= 86400) {\n            const days = Math.floor(numericTTL / 86400);\n            const remainder = numericTTL % 86400;\n            if (remainder === 0) {\n                return \"\".concat(days, \" day\").concat(days !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 3600) {\n            const hours = Math.floor(numericTTL / 3600);\n            const remainder = numericTTL % 3600;\n            if (remainder === 0) {\n                return \"\".concat(hours, \" hour\").concat(hours !== 1 ? \"s\" : \"\");\n            }\n        } else if (numericTTL >= 60) {\n            const minutes = Math.floor(numericTTL / 60);\n            const remainder = numericTTL % 60;\n            if (remainder === 0) {\n                return \"\".concat(minutes, \" minute\").concat(minutes !== 1 ? \"s\" : \"\");\n            }\n        }\n        return \"\".concat(numericTTL, \"s\");\n    };\n    // Format record name\n    const formatRecordName = (name)=>{\n        if (!name || name === \"@\") return (domain === null || domain === void 0 ? void 0 : domain.name) || \"@\";\n        if (name.endsWith(\".\")) return name.slice(0, -1);\n        return name;\n    };\n    // Copy to clipboard\n    const copyToClipboard = async (text)=>{\n        try {\n            await navigator.clipboard.writeText(text);\n        } catch (err) {\n            console.error(\"Failed to copy:\", err);\n        }\n    };\n    // Handle delete confirmation\n    const handleDeleteClick = (record)=>{\n        setDeleteConfirm(record);\n    };\n    const handleDeleteConfirm = ()=>{\n        if (deleteConfirm && onDelete) {\n            onDelete(deleteConfirm.id, deleteConfirm);\n        }\n        setDeleteConfirm(null);\n    };\n    const config = getRecordTypeConfig(recordType);\n    const IconComponent = config.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 rounded-lg \".concat(config.bgColor, \" border border-gray-200\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                            className: \"h-5 w-5 \".concat(config.textColor)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900\",\n                                                children: [\n                                                    recordType,\n                                                    \" Records\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    config.label,\n                                                    \" • \",\n                                                    records.length,\n                                                    \" record\",\n                                                    records.length !== 1 ? \"s\" : \"\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            !showInlineForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAddClick,\n                                className: \"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add \",\n                                    recordType\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this),\n                    showInlineForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-blue-50 border border-blue-200 rounded-lg p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-medium text-blue-900\",\n                                            children: [\n                                                \"Add New \",\n                                                recordType,\n                                                \" Record\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this),\n                                (()=>{\n                                    const recordTypeConfig = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DNS_RECORD_TYPES[recordType];\n                                    if (!recordTypeConfig) return null;\n                                    const fields = recordTypeConfig.fields;\n                                    const gridCols = fields.length <= 3 ? \"md:grid-cols-\".concat(fields.length) : \"md:grid-cols-3\";\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 \".concat(gridCols, \" gap-4\"),\n                                        children: fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: [\n                                                            field.label,\n                                                            field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-red-500 ml-1\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 46\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    field.name === \"ttl\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData[field.name] || field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL,\n                                                        onChange: (e)=>handleFieldChange(field.name, parseInt(e.target.value) || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL),\n                                                        placeholder: \"TTL in seconds\",\n                                                        min: \"60\",\n                                                        max: \"2147483647\",\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 27\n                                                    }, this) : field.type === \"number\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, parseInt(e.target.value) || 0),\n                                                        placeholder: field.placeholder,\n                                                        min: field.min,\n                                                        max: field.max,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 27\n                                                    }, this) : field.type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                                                        placeholder: field.placeholder,\n                                                        rows: 2,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 27\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData[field.name] || \"\",\n                                                        onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                                                        placeholder: field.placeholder,\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    formErrors[field.name] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mt-1 text-sm text-red-600\",\n                                                        children: formErrors[field.name]\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 19\n                                    }, this);\n                                })(),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between pt-4 border-t border-blue-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-blue-700\",\n                                            children: [\n                                                'Use \"@\" for root domain (',\n                                                domain === null || domain === void 0 ? void 0 : domain.name,\n                                                '), \"www\" for www.',\n                                                domain === null || domain === void 0 ? void 0 : domain.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: handleCancel,\n                                                    disabled: isSubmitting,\n                                                    className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2 inline\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Cancel\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: isSubmitting,\n                                                    className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\",\n                                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"animate-spin h-4 w-4 mr-2 inline\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        className: \"opacity-25\",\n                                                                        cx: \"12\",\n                                                                        cy: \"12\",\n                                                                        r: \"10\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        className: \"opacity-75\",\n                                                                        fill: \"currentColor\",\n                                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 437,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Adding...\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2 inline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Add Record\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 344,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-gray-500 bg-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 15\n                                }, this),\n                                \"Loading \",\n                                recordType,\n                                \" records...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 457,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 456,\n                        columnNumber: 11\n                    }, this) : records.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-16\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 rounded-full \".concat(config.bgColor),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                        className: \"h-8 w-8 \".concat(config.textColor)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: [\n                                                \"No \",\n                                                recordType,\n                                                \" records found\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mb-4\",\n                                            children: [\n                                                \"Create your first \",\n                                                recordType,\n                                                \" record to get started\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddClick,\n                                            className: \"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Add \",\n                                                recordType,\n                                                \" Record\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 466,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"min-w-full divide-y divide-gray-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Content\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 496,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"TTL\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 499,\n                                                columnNumber: 19\n                                            }, this),\n                                            (recordType === \"MX\" || recordType === \"SRV\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Priority\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                scope: \"col\",\n                                                className: \"relative px-6 py-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"sr-only\",\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"bg-white divide-y divide-gray-200\",\n                                    children: records.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                            children: [\n                                                editingRecordId !== record.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50 transition-colors duration-150\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: formatRecordName(record.name)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 522,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-mono text-gray-700 flex-1 min-w-0 truncate\",\n                                                                        children: record.content\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 530,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>copyToClipboard(record.content),\n                                                                        className: \"p-1 text-gray-400 hover:text-gray-600 rounded\",\n                                                                        title: \"Copy to clipboard\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                            lineNumber: 538,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 533,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    formatTTL(record.ttl)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (recordType === \"MX\" || recordType === \"SRV\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-900\",\n                                                                children: record.priority || \"-\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-end gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleEditClick(record),\n                                                                        className: \"p-2 text-gray-400 hover:text-blue-600 rounded-md hover:bg-blue-50\",\n                                                                        title: \"Edit record\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                            lineNumber: 568,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 563,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDeleteClick(record),\n                                                                        className: \"p-2 text-gray-400 hover:text-red-600 rounded-md hover:bg-red-50\",\n                                                                        title: \"Delete record\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                lineNumber: 562,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 23\n                                                }, this),\n                                                editingRecordId === record.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"bg-blue-50\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                        colSpan: recordType === \"MX\" || recordType === \"SRV\" ? 5 : 4,\n                                                        className: \"px-6 py-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                            onSubmit: handleEditSubmit,\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 mb-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                            lineNumber: 588,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-lg font-medium text-blue-900\",\n                                                                            children: [\n                                                                                \"Edit \",\n                                                                                recordType,\n                                                                                \" Record\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                (()=>{\n                                                                    const recordTypeConfig = _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DNS_RECORD_TYPES[recordType];\n                                                                    if (!recordTypeConfig) return null;\n                                                                    const fields = recordTypeConfig.fields;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                                                        children: fields.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                                                        children: [\n                                                                                            field.label,\n                                                                                            field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-red-500 ml-1\",\n                                                                                                children: \"*\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                                                lineNumber: 607,\n                                                                                                columnNumber: 60\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                                        lineNumber: 605,\n                                                                                        columnNumber: 39\n                                                                                    }, this),\n                                                                                    field.name === \"ttl\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"number\",\n                                                                                        value: editFormData[field.name] || field.default || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL,\n                                                                                        onChange: (e)=>handleEditFieldChange(field.name, parseInt(e.target.value) || _constants_dnsRecords__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TTL),\n                                                                                        placeholder: \"TTL in seconds\",\n                                                                                        min: \"60\",\n                                                                                        max: \"2147483647\",\n                                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                                        lineNumber: 610,\n                                                                                        columnNumber: 41\n                                                                                    }, this) : field.type === \"number\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"number\",\n                                                                                        value: editFormData[field.name] || \"\",\n                                                                                        onChange: (e)=>handleEditFieldChange(field.name, parseInt(e.target.value) || 0),\n                                                                                        placeholder: field.placeholder,\n                                                                                        min: field.min,\n                                                                                        max: field.max,\n                                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                                        lineNumber: 620,\n                                                                                        columnNumber: 41\n                                                                                    }, this) : field.type === \"textarea\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                                        value: editFormData[field.name] || \"\",\n                                                                                        onChange: (e)=>handleEditFieldChange(field.name, e.target.value),\n                                                                                        placeholder: field.placeholder,\n                                                                                        rows: 2,\n                                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                                        lineNumber: 630,\n                                                                                        columnNumber: 41\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"text\",\n                                                                                        value: editFormData[field.name] || \"\",\n                                                                                        onChange: (e)=>handleEditFieldChange(field.name, e.target.value),\n                                                                                        placeholder: field.placeholder,\n                                                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                                        lineNumber: 638,\n                                                                                        columnNumber: 41\n                                                                                    }, this),\n                                                                                    editFormErrors[field.name] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"mt-1 text-sm text-red-600\",\n                                                                                        children: editFormErrors[field.name]\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                                        lineNumber: 647,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, field.name, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                                lineNumber: 604,\n                                                                                columnNumber: 37\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 33\n                                                                    }, this);\n                                                                })(),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-end gap-3 pt-4 border-t border-blue-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"button\",\n                                                                            onClick: handleEditCancel,\n                                                                            className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                                            children: \"Cancel\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                            lineNumber: 657,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            type: \"submit\",\n                                                                            disabled: isEditSubmitting,\n                                                                            className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                                            children: isEditSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                                        lineNumber: 671,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Updating...\"\n                                                                                ]\n                                                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                        className: \"h-4 w-4 mr-2\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                                        lineNumber: 676,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Update Record\"\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                            lineNumber: 664,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                            lineNumber: 586,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, record.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                            lineNumber: 490,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 489,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            deleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Copy_Edit_FileText_Globe_Info_Link_Mail_Plus_Save_Server_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-6 w-6 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center sm:text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg leading-6 font-medium text-gray-900\",\n                                            children: \"Delete DNS Record\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 699,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mb-4\",\n                                        children: [\n                                            \"Are you sure you want to delete this \",\n                                            deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.type,\n                                            \" record? This action cannot be undone.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-gray-50 rounded-lg border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-gray-900 mb-1\",\n                                                    children: formatRecordName(deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.name)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-mono text-gray-600\",\n                                                    children: deleteConfirm === null || deleteConfirm === void 0 ? void 0 : deleteConfirm.content\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 709,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3 mt-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setDeleteConfirm(null),\n                                        className: \"flex-1 px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleDeleteConfirm,\n                                        className: \"flex-1 px-4 py-2 bg-red-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\",\n                                        children: \"Delete Record\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                                lineNumber: 724,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                        lineNumber: 698,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                    lineNumber: 697,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\DnsRecordTable.jsx\",\n                lineNumber: 696,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(DnsRecordTable, \"jg6ldIq40+iCHRKQQt63Kt0bQRg=\");\n_c = DnsRecordTable;\nvar _c;\n$RefreshReg$(_c, \"DnsRecordTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/DnsRecordTable.jsx\n"));

/***/ })

});
"use client";
import { useState, useEffect } from "react";
import {
  Globe,
  Link,
  Mail,
  FileText,
  Server,
  Settings,
  AlertCircle,
  RefreshCw,
  CheckCircle,
  Clock,
} from "lucide-react";
import DnsRecordTable from "./DnsRecordTable";

import domainMngService from "@/app/services/domainMngService";
import { toast } from "react-toastify";

export default function ImprovedDnsManager({ domain, onUpdate }) {
  const [dnsServiceActive, setDnsServiceActive] = useState(
    domain?.dnsActivated || false
  );
  const [allDnsRecords, setAllDnsRecords] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activatingService, setActivatingService] = useState(false);


  const [activeTab, setActiveTab] = useState("A");

  // DNS record types configuration
  const recordTypes = [
    { type: "A", label: "A", icon: Globe, description: "IPv4 addresses" },
    { type: "AAAA", label: "AAAA", icon: Globe, description: "IPv6 addresses" },
    {
      type: "CNAME",
      label: "CNAME",
      icon: Link,
      description: "Canonical names",
    },
    { type: "MX", label: "MX", icon: Mail, description: "Mail servers" },
    { type: "TXT", label: "TXT", icon: FileText, description: "Text records" },
    { type: "NS", label: "NS", icon: Server, description: "Name servers" },
    {
      type: "SRV",
      label: "SRV",
      icon: Settings,
      description: "Service records",
    },
  ];

  // Load DNS records
  const loadDnsRecords = async () => {
    try {
      setLoading(true);
      console.log(
        `🔍 Loading DNS records for domain: ${domain?.name} (ID: ${domain?.id})`
      );

      const response = await domainMngService.getDnsRecords(domain.id);
      console.log(`📋 DNS Records Response:`, response.data);

      if (response.data.success) {
        const records = response.data.records || [];
        setAllDnsRecords(records);
        console.log(`✅ Loaded ${records.length} DNS records`);
        // Don't automatically set DNS service as active just because records loaded
        // The activation status should come from the domain data, not from successful record loading

        // Log records by type for debugging
        recordTypes.forEach(({ type }) => {
          const typeRecords = records.filter((r) => r.type === type);
          console.log(
            `📊 ${type} Records (${typeRecords.length}):`,
            typeRecords
          );
        });
      } else {
        throw new Error(response.data.error || "Failed to load DNS records");
      }
    } catch (error) {
      console.error("❌ Error loading DNS records:", error);
      console.error("❌ Error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });

      // Don't change DNS service activation status based on record loading failure
      // The activation status should only be determined by domain.dnsActivated
      toast.error(
        "Failed to load DNS records. Please check if DNS service is activated."
      );
      setAllDnsRecords([]);
    } finally {
      setLoading(false);
    }
  };

  // Activate DNS service
  const activateDnsService = async () => {
    try {
      setActivatingService(true);
      console.log(`🚀 Activating DNS service for domain: ${domain?.name}`);
      console.log(`🔍 Domain object:`, domain);

      // The backend expects orderId, which should be the domain's order ID
      const orderId = domain.orderid || domain.domainOrderId || domain.id;
      console.log(`📋 Using order ID: ${orderId}`);

      const response = await domainMngService.activateDnsService(orderId);

      console.log(`📋 DNS Activation Response:`, response.data);

      if (response.data.success) {
        setDnsServiceActive(true);
        toast.success("DNS service activated successfully!");

        // Update the domain object with DNS activation status
        if (onUpdate) {
          onUpdate({
            dnsActivated: true,
            dnsActivatedAt: new Date().toISOString(),
            dnsZoneId: response.data.zoneId || null,
          });
        }

        await loadDnsRecords(); // Load records after activation
      } else {
        throw new Error(
          response.data.error || "Failed to activate DNS service"
        );
      }
    } catch (error) {
      console.error("❌ Error activating DNS service:", error);
      toast.error("Failed to activate DNS service");
    } finally {
      setActivatingService(false);
    }
  };



  // Add DNS record
  const handleAddRecord = async (recordData) => {
    try {
      console.log(`➕ Adding DNS record:`, recordData);

      const response = await domainMngService.addDnsRecord(
        domain.id,
        recordData
      );
      console.log(`📋 Add Record Response:`, response.data);

      if (response.data.success) {
        toast.success(`${recordData.type} record added successfully!`);
        await loadDnsRecords(); // Reload records
      } else {
        throw new Error(response.data.error || "Failed to add DNS record");
      }
    } catch (error) {
      console.error("❌ Error adding DNS record:", error);
      toast.error("Failed to add DNS record");
    }
  };

  // Edit DNS record
  const handleEditRecord = async (recordData) => {
    try {
      console.log(`✏️ Editing DNS record:`, recordData);

      const response = await domainMngService.updateDnsRecord(
        domain.id,
        recordData.id,
        recordData
      );

      console.log(`📋 Edit Record Response:`, response.data);

      if (response.data.success) {
        toast.success(`${recordData.type} record updated successfully!`);
        await loadDnsRecords(); // Reload records
      } else {
        throw new Error(response.data.error || "Failed to update DNS record");
      }
    } catch (error) {
      console.error("❌ Error updating DNS record:", error);
      toast.error("Failed to update DNS record");
    }
  };

  // Delete DNS record
  const handleDeleteRecord = async (recordId, record = null) => {
    try {
      console.log(`🗑️ Deleting DNS record ID: ${recordId}`, record);

      const response = await domainMngService.deleteDnsRecord(
        domain.id,
        recordId,
        record
      );
      console.log(`📋 Delete Record Response:`, response.data);

      if (response.data.success) {
        toast.success("DNS record deleted successfully!");
        await loadDnsRecords(); // Reload records
      } else {
        throw new Error(response.data.error || "Failed to delete DNS record");
      }
    } catch (error) {
      console.error("❌ Error deleting DNS record:", error);
      toast.error("Failed to delete DNS record");
    }
  };

  // Get records for specific type (filter out empty records)
  const getRecordsForType = (type) => {
    return allDnsRecords.filter(
      (record) =>
        record.type === type && record.content && record.content.trim() !== ""
    );
  };



  // Update DNS service status when domain changes
  useEffect(() => {
    setDnsServiceActive(domain?.dnsActivated || false);
  }, [domain?.dnsActivated]);

  useEffect(() => {
    if (domain?.id) {
      loadDnsRecords();
    }
  }, [domain?.id]);

  if (!domain) {
    return (
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-center gap-3">
        <AlertCircle className="h-5 w-5 text-amber-600 flex-shrink-0" />
        {/* <p className="text-amber-800">Domain information is required to manage DNS records.</p> */}
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200 px-6 py-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">DNS Management</h1>
            <div className="flex items-center gap-3 mt-2">
              <p className="text-gray-600">Manage DNS records for</p>
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                {domain.name}
              </span>
              <div className="flex items-center gap-2">
                {dnsServiceActive ? (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-600 font-medium">DNS Active</span>
                  </>
                ) : (
                  <>
                    <Clock className="h-4 w-4 text-amber-600" />
                    <span className="text-sm text-amber-600 font-medium">DNS Inactive</span>
                  </>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={loadDnsRecords}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </button>

          </div>
        </div>
      </div>

      {/* DNS Service Status */}
      {!dnsServiceActive && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-6 mx-6 mt-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-lg font-medium text-amber-900 mb-1">
                  DNS Service Not Active
                </h3>
                <p className="text-amber-800 text-sm leading-relaxed">
                  You need to activate DNS service before you can manage DNS records for this domain.
                  Once activated, you'll be able to add, edit, and delete DNS records.
                </p>
              </div>
            </div>
            <button
              onClick={activateDnsService}
              disabled={activatingService}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-amber-600 hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 disabled:opacity-50 disabled:cursor-not-allowed flex-shrink-0"
            >
              {activatingService && (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              )}
              Activate DNS
            </button>
          </div>
        </div>
      )}

      {/* Main Content Area */}
      <div className="px-6 pb-6">
        {/* Message when DNS is not active - shown instead of records table */}
        {!dnsServiceActive && (
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm mt-6">
            <div className="text-center py-16">
              <div className="flex flex-col items-center gap-6">
                <div className="w-20 h-20 bg-amber-100 rounded-full flex items-center justify-center">
                  <AlertCircle className="h-10 w-10 text-amber-600" />
                </div>
                <div className="max-w-md">
                  <h3 className="text-xl font-medium text-gray-900 mb-3">
                    DNS Management Not Available
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    DNS service is not activated for this domain. Please activate DNS service
                    using the button above to start managing your DNS records.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* DNS Records Section - Only show when DNS service is active */}
        {dnsServiceActive && (
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm mt-6">
            {/* Tab Navigation */}
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6" aria-label="Tabs">
                {recordTypes.map(({ type, label }) => {
                  const count = getRecordsForType(type).length;
                  const isActive = activeTab === type;
                  return (
                    <button
                      key={type}
                      onClick={() => setActiveTab(type)}
                      className={`${
                        isActive
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2`}
                    >
                      <span>{label}</span>
                      {count > 0 && (
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          isActive ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {count}
                        </span>
                      )}
                    </button>
                  );
                })}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              <DnsRecordTable
                key={activeTab}
                records={getRecordsForType(activeTab)}
                recordType={activeTab}
                onEdit={handleEditRecord}
                onDelete={handleDeleteRecord}
                onAdd={handleAddRecord}
                domain={domain}
                loading={loading}
              />
            </div>
          </div>
        )}
      </div>




    </div>
  );
}

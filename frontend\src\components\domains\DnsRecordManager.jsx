"use client";
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Card<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>bsBody,
  <PERSON>b,
  <PERSON>b<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
} from "@material-tailwind/react";
import {
  Plus,
  Globe,
  Mail,
  FileText,
  Server,
  Link,
  Settings,
  AlertCircle,
  Zap,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import DnsRecordForm from "./DnsRecordForm";
import DnsRecordList from "./DnsRecordList";
import DnsPresetSelector from "./DnsPresetSelector";
import { getDnsRecordTypes } from "@/constants/dnsRecords";
import { toast } from "react-toastify";

export default function DnsRecordManager({ domain, onUpdate }) {
  const [dnsRecords, setDnsRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("all");
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingRecord, setEditingRecord] = useState(null);
  const [selectedRecordType, setSelectedRecordType] = useState("A");

  // Load DNS records
  const loadDnsRecords = async () => {
    try {
      setLoading(true);

      const response = await domainMngService.getDnsRecords(domain.id);
      if (response.data.success) {
        setDnsRecords(response.data.records || []);
      } else {
        throw new Error(response.data.error || "Failed to load DNS records");
      }
    } catch (error) {
      console.error("Error loading DNS records:", error);
      toast.error("Failed to load DNS records");
      setDnsRecords([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (domain?.id) {
      loadDnsRecords();
    }
  }, [domain?.id]);

  // Handle adding a new DNS record
  const handleAddRecord = async (recordData) => {
    try {
      const response = await domainMngService.addDnsRecord(domain.id, recordData);
      if (response.data.success) {
        await loadDnsRecords(); // Reload to get updated data
        setShowAddForm(false);
        toast.success("DNS record added successfully");
      } else {
        throw new Error(response.data.error || "Failed to add DNS record");
      }
    } catch (error) {
      console.error("Error adding DNS record:", error);
      toast.error("Failed to add DNS record");
    }
  };

  // Handle updating a DNS record
  const handleUpdateRecord = async (recordId, recordData) => {
    try {
      const response = await domainMngService.updateDnsRecord(domain.id, recordId, recordData);
      if (response.data.success) {
        await loadDnsRecords(); // Reload to get updated data
        setEditingRecord(null);
        toast.success("DNS record updated successfully");
      } else {
        throw new Error(response.data.error || "Failed to update DNS record");
      }
    } catch (error) {
      console.error("Error updating DNS record:", error);

      // Handle locked records specifically
      if (error.response?.status === 423 || error.response?.data?.locked) {
        toast.error(
          "🔒 This DNS record is locked by the registrar and cannot be modified. Default nameserver records are typically locked to prevent accidental deletion of critical DNS infrastructure.",
          { autoClose: 8000 }
        );
      } else {
        toast.error(
          error.response?.data?.message ||
          error.response?.data?.error ||
          "Failed to update DNS record"
        );
      }
    }
  };

  // Handle deleting a DNS record
  const handleDeleteRecord = async (recordId, record = null) => {
    try {
      const response = await domainMngService.deleteDnsRecord(domain.id, recordId, record);
      if (response.data.success) {
        await loadDnsRecords(); // Reload to get updated data
        toast.success("DNS record deleted successfully");
      } else {
        throw new Error(response.data.error || "Failed to delete DNS record");
      }
    } catch (error) {
      console.error("Error deleting DNS record:", error);

      // Handle locked records specifically
      if (error.response?.status === 423 || error.response?.data?.locked) {
        toast.error(
          "🔒 This DNS record is locked by the registrar and cannot be deleted. Default nameserver records are typically locked to prevent accidental deletion of critical DNS infrastructure.",
          { autoClose: 8000 }
        );
      } else {
        toast.error(
          error.response?.data?.message ||
          error.response?.data?.error ||
          "Failed to delete DNS record"
        );
      }
    }
  };

  // Handle applying DNS presets
  const handleApplyPreset = async (presetRecords) => {
    try {
      // TODO: Implement batch record creation
      for (const record of presetRecords) {
        // Replace {domain} placeholder with actual domain name
        const processedRecord = {
          ...record,
          content: record.content.replace("{domain}", domain.name),
        };
        await handleAddRecord(processedRecord);
      }
      toast.success("DNS preset applied successfully");
    } catch (error) {
      console.error("Error applying DNS preset:", error);
      toast.error("Failed to apply DNS preset");
    }
  };

  // Filter records by type
  const getFilteredRecords = () => {
    if (activeTab === "all") {
      return dnsRecords;
    }
    return dnsRecords.filter((record) => record.type === activeTab);
  };

  // Get record type icons
  const getRecordTypeIcon = (type) => {
    const icons = {
      A: Globe,
      AAAA: Globe,
      CNAME: Link,
      MX: Mail,
      TXT: FileText,
      NS: Server,
      SRV: Settings,
    };
    return icons[type] || Globe;
  };

  // Get record type counts
  const getRecordTypeCounts = () => {
    const counts = {};
    dnsRecords.forEach((record) => {
      counts[record.type] = (counts[record.type] || 0) + 1;
    });
    return counts;
  };

  const recordTypes = getDnsRecordTypes();
  const recordCounts = getRecordTypeCounts();

  // Tab data
  const tabsData = [
    { label: "All Records", value: "all", count: dnsRecords.length },
    ...recordTypes.map((type) => ({
      label: type.label,
      value: type.value,
      count: recordCounts[type.value] || 0,
    })),
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Spinner className="h-8 w-8" />
        <Typography className="ml-2 text-gray-600">
          Loading DNS records...
        </Typography>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="flex flex-wrap gap-3">
        <Button
          className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
          onClick={() => {
            setSelectedRecordType("A");
            setShowAddForm(true);
          }}
        >
          <Plus className="h-4 w-4" />
          Add DNS Record
        </Button>

        <DnsPresetSelector onApplyPreset={handleApplyPreset} />
      </div>

      {/* DNS Records Tabs */}
      <Card>
        <CardBody className="p-0">
          <Tabs value={activeTab} onChange={setActiveTab}>
            <TabsHeader className="bg-gray-50 p-1 m-6 mb-0">
              {tabsData.map(({ label, value, count }) => (
                <Tab key={value} value={value} className="flex items-center gap-2">
                  <span>{label}</span>
                  {count > 0 && (
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                      {count}
                    </span>
                  )}
                </Tab>
              ))}
            </TabsHeader>

            <TabsBody className="p-6">
              <TabPanel value={activeTab} className="p-0">
                <DnsRecordList
                  records={getFilteredRecords()}
                  onEdit={setEditingRecord}
                  onDelete={handleDeleteRecord}
                  domain={domain}
                />
              </TabPanel>
            </TabsBody>
          </Tabs>
        </CardBody>
      </Card>

      {/* Add/Edit Record Form Modal */}
      {(showAddForm || editingRecord) && (
        <DnsRecordForm
          isOpen={showAddForm || !!editingRecord}
          onClose={() => {
            setShowAddForm(false);
            setEditingRecord(null);
          }}
          onSubmit={editingRecord ?
            (data) => handleUpdateRecord(editingRecord.id, data) :
            handleAddRecord
          }
          initialData={editingRecord}
          domain={domain}
          selectedType={selectedRecordType}
          onTypeChange={setSelectedRecordType}
        />
      )}

      {/* Empty State */}
      {dnsRecords.length === 0 && (
        <Alert color="blue" className="text-center">
          <AlertCircle className="h-4 w-4 mx-auto mb-2" />
          <Typography className="font-semibold mb-2">No DNS Records Found</Typography>
          <Typography className="text-sm mb-4">
            Get started by adding your first DNS record or applying a preset configuration.
          </Typography>
          <div className="flex justify-center gap-2">
            <Button
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
              onClick={() => setShowAddForm(true)}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Record
            </Button>
          </div>
        </Alert>
      )}
    </div>
  );
}

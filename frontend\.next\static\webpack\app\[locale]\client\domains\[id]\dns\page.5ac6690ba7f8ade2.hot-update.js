"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/dns/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/dns/page.jsx":
/*!***********************************************************!*\
  !*** ./src/app/[locale]/client/domains/[id]/dns/page.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DomainDnsManagementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Globe,Info,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Globe,Info,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Globe,Info,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Globe,Info,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Globe,Info,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Globe,Info,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Globe,Info,Server,Shield,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_ImprovedDnsManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/domains/ImprovedDnsManager */ \"(app-pages-browser)/./src/components/domains/ImprovedDnsManager.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction DomainDnsManagementPage() {\n    _s();\n    const { id } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations)(\"client.domainWrapper\");\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"records\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Function to update domain data\n    const updateDomain = (updatedDomain)=>{\n        setDomain((prevDomain)=>({\n                ...prevDomain,\n                ...updatedDomain\n            }));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchDomainData = async ()=>{\n            try {\n                setLoading(true);\n                setError(null);\n                // Get domain data from user domains\n                const domainsResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserDomains();\n                if (domainsResponse.data && domainsResponse.data.domains) {\n                    const foundDomain = domainsResponse.data.domains.find((d)=>d.id === id);\n                    if (!foundDomain) {\n                        setError(\"Domain not found\");\n                        return;\n                    }\n                    // Get detailed domain information\n                    try {\n                        const detailsResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getDomainDetailsByName(foundDomain.name, \"All\");\n                        if (detailsResponse.data.success && detailsResponse.data.domain) {\n                            const domainDetails = detailsResponse.data.domain;\n                            // Merge domain data with details\n                            const enrichedDomain = {\n                                ...foundDomain,\n                                ...domainDetails,\n                                // Ensure we keep the original ID\n                                id: foundDomain.id\n                            };\n                            setDomain(enrichedDomain);\n                        } else {\n                            // Use basic domain data if details fetch fails\n                            setDomain(foundDomain);\n                        }\n                    } catch (detailsError) {\n                        console.warn(\"Could not fetch domain details:\", detailsError);\n                        // Use basic domain data if details fetch fails\n                        setDomain(foundDomain);\n                    }\n                } else {\n                    setError(\"Failed to load domain data\");\n                }\n            } catch (error) {\n                console.error(\"Error fetching domain data:\", error);\n                setError(\"Failed to load domain information\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_6__.toast.error(\"Failed to load domain information\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        if (id) {\n            fetchDomainData();\n        }\n    }, [\n        id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (domain === null || domain === void 0 ? void 0 : domain.orderid) {\n            console.log(\"Order ID available:\", domain.orderid);\n        } else {\n            console.warn(\"Order ID is missing for domain:\", domain);\n        }\n    }, [\n        domain\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-[60vh]\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                className: \"text-gray-600\",\n                                children: t(\"loading\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 134,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !domain) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8 bg-gray-50 min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"text\",\n                        className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                        onClick: ()=>router.push(\"/client/domains\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this),\n                            dt(\"back_to_domains\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Alert, {\n                        color: \"red\",\n                        className: \"max-w-md mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            error || \"Domain not found\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Breadcrumbs, {\n                                    className: \"bg-white py-2 px-3 rounded-lg border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"text\",\n                                            className: \"text-blue-600 hover:text-blue-800 p-0\",\n                                            onClick: ()=>router.push(\"/client/domains\"),\n                                            children: t(\"domains\", {\n                                                defaultValue: \"Domains\"\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                            className: \"text-gray-600\",\n                                            children: domain.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-gray-800 font-medium\",\n                                                    children: t(\"dns_management\", {\n                                                        defaultValue: \"DNS Management\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-2 w-2 rounded-full \".concat(domain.status === \"active\" || domain.currentstatus === \"Active\" ? \"bg-green-500\" : domain.status === \"pending\" ? \"bg-yellow-500\" : \"bg-gray-400\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    variant: \"h4\",\n                                                    className: \"text-gray-900\",\n                                                    children: domain.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, this),\n                                                domain.domainOrderId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        \"ID: \",\n                                                        domain.domainOrderId\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    content: \"Domain Security\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-green-50 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                    content: \"DNS Status\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-2 bg-blue-50 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                            className: \"text-gray-600\",\n                            children: t(\"manage_dns_settings_description\", {\n                                defaultValue: \"Configure DNS records and nameservers for your domain. Changes may take up to 24-48 hours to propagate globally.\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"bg-white border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsHeader, {\n                                    className: \"bg-gray-50 border-b\",\n                                    indicatorProps: {\n                                        className: \"bg-transparent\"\n                                    },\n                                    children: [\n                                        {\n                                            label: \"DNS Records\",\n                                            value: \"records\",\n                                            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                            description: \"Set up where your domain points to\"\n                                        },\n                                        {\n                                            label: \"Nameservers\",\n                                            value: \"nameservers\",\n                                            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                            description: \"Configure your domain's DNS servers\"\n                                        }\n                                    ].map((param)=>{\n                                        let { label, value, icon: Icon, description } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tab, {\n                                            value: value,\n                                            className: \"flex items-center justify-center gap-2 px-6 py-3 relative text-gray-600 hover:text-gray-900\",\n                                            onClick: ()=>setActiveTab(value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center gap-2 \".concat(value === activeTab ? \"text-blue-500\" : \"\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 21\n                                                }, this),\n                                                value === activeTab && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 left-0 w-full h-0.5 bg-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, value, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabsBody, {\n                                    className: \"relative min-h-[400px]\",\n                                    children: [\n                                        {\n                                            value: \"records\",\n                                            title: \"DNS Records\",\n                                            description: \"DNS records tell the internet where to find your website, email, and other services\",\n                                            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_ImprovedDnsManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: updateDomain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 23\n                                            }, this)\n                                        },\n                                        {\n                                            value: \"nameservers\",\n                                            title: \"Nameservers\",\n                                            description: \"Nameservers are the internet's address book for your domain\",\n                                            guide: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 mb-6 bg-blue-50 border border-blue-100 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 bg-blue-100 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"font-medium text-blue-900 mb-1\",\n                                                                    children: \"About Nameservers\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                    className: \"text-sm text-blue-800\",\n                                                                    children: \"Nameservers control who manages your domain's DNS settings. Only change these if you're moving to a different DNS provider.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 23\n                                            }, this),\n                                            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                domain: domain,\n                                                onUpdate: updateDomain\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 23\n                                            }, this)\n                                        }\n                                    ].map((param)=>{\n                                        let { value, title, description, guide, component } = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.TabPanel, {\n                                            value: value,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                        variant: \"h4\",\n                                                                        className: \"text-gray-800\",\n                                                                        children: title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                                                        content: description,\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            guide\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    component\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, value, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 bg-blue-50 border border-blue-100 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 text-blue-600 mt-0.5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 349,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                        className: \"font-medium text-blue-900 mb-2\",\n                                        children: \"Important DNS Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm space-y-2 text-blue-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"DNS changes take 24-48 hours to propagate globally\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Always backup your current DNS settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Globe_Info_Server_Shield_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Use Health Check to verify your setup\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                        lineNumber: 348,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\[id]\\\\dns\\\\page.jsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n_s(DomainDnsManagementPage, \"PPmgeJW0xHd6YO6+cvmPTeSqUBY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_8__.useTranslations\n    ];\n});\n_c = DomainDnsManagementPage;\nvar _c;\n$RefreshReg$(_c, \"DomainDnsManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/[id]/dns/page.jsx\n"));

/***/ })

});